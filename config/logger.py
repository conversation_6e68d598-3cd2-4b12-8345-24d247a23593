import os
import sys
import logging
from loguru import logger
from config.config_loader import load_config
from config.settings import check_config_file

SERVER_VERSION = "0.4.3"


def get_module_abbreviation(module_name, module_dict):
    """获取模块名称的缩写，如果为空则返回00
    如果名称中包含下划线，则返回下划线后面的前两个字符
    """
    module_value = module_dict.get(module_name, "")
    if not module_value:
        return "00"
    if "_" in module_value:
        parts = module_value.split("_")
        return parts[-1][:2] if parts[-1] else "00"
    return module_value[:2]


def build_module_string(selected_module):
    """构建模块字符串"""
    return (
        get_module_abbreviation("VAD", selected_module)
        + get_module_abbreviation("ASR", selected_module)
        + get_module_abbreviation("LLM", selected_module)
        + get_module_abbreviation("TTS", selected_module)
        + get_module_abbreviation("Memory", selected_module)
        + get_module_abbreviation("Intent", selected_module)
    )


def formatter(record):
    """为没有 tag 的日志添加默认值"""
    record["extra"].setdefault("tag", record["name"])
    return record["message"]


def binary_log_filter(record):
    """过滤BINARY日志的过滤器"""
    # 过滤包含BINARY的日志消息
    message = record.get("message", "")
    if "BINARY" in message:
        return False
    return True


def setup_logging():
    check_config_file()
    """从配置文件中读取日志配置，并设置日志输出格式和级别"""
    config = load_config()
    log_config = config["log"]
    log_format = log_config.get(
        "log_format",
        "<green>{time:YYMMDD HH:mm:ss}</green>[{version}_{selected_module}][<light-blue>{extra[tag]}</light-blue>]-<level>{level}</level>-<light-green>{message}</light-green>",
    )
    log_format_file = log_config.get(
        "log_format_file",
        "{time:YYYY-MM-DD HH:mm:ss} - {version_{selected_module}} - {name} - {level} - {extra[tag]} - {message}",
    )
    selected_module_str = build_module_string(config.get("selected_module", {}))

    log_format = log_format.replace("{version}", SERVER_VERSION)
    log_format = log_format.replace("{selected_module}", selected_module_str)
    log_format_file = log_format_file.replace("{version}", SERVER_VERSION)
    log_format_file = log_format_file.replace("{selected_module}", selected_module_str)

    log_level = log_config.get("log_level", "INFO")
    log_dir = log_config.get("log_dir", "tmp")
    log_file = log_config.get("log_file", "server.log")
    data_dir = log_config.get("data_dir", "data")

    os.makedirs(log_dir, exist_ok=True)
    os.makedirs(data_dir, exist_ok=True)

    # 配置日志输出
    logger.remove()

    # 输出到控制台
    logger.add(sys.stdout, format=log_format, level=log_level, filter=lambda record: formatter(record) and binary_log_filter(record))

    # 输出到文件 - 添加日志轮转和保留设置
    logger.add(
        os.path.join(log_dir, log_file),
        format=log_format_file,
        level=log_level,
        filter=lambda record: formatter(record) and binary_log_filter(record),
        rotation="10 MB",  # 文件达到10MB时轮转
        retention="7 days",  # 保留7天的日志文件
        compression="zip",  # 压缩旧日志文件
        enqueue=True,  # 异步写入，避免阻塞
    )

    # 配置标准库日志记录器输出DEBUG级别日志
    logging.basicConfig(level=logging.INFO, handlers=[])
    
    # 设置所有第三方库的日志级别为DEBUG并重定向到loguru
    class InterceptHandler(logging.Handler):
        def emit(self, record):
            # 过滤BINARY日志
            message = record.getMessage()
            if "BINARY" in message:
                return

            try:
                level = logger.level(record.levelname).name
            except ValueError:
                level = record.levelno

            frame, depth = logging.currentframe(), 2
            while frame.f_code.co_filename == logging.__file__:
                frame = frame.f_back
                depth += 1

            logger.opt(depth=depth, exception=record.exc_info).log(level, message)

    # 移除所有现有的处理器并添加拦截处理器
    logging.root.handlers = [InterceptHandler()]
    logging.root.setLevel(logging.INFO)
    
    # 为所有已存在的logger设置DEBUG级别
    for name in logging.Logger.manager.loggerDict:
        logging.getLogger(name).setLevel(logging.INFO)

    return logger
