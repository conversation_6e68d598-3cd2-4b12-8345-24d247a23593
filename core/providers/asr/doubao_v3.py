import asyncio
import threading
import websockets
import json
import gzip
import struct
import time
import uuid
from typing import Dict, Optional, Callable, Any
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()

# Protocol constants from Java demo
PROTOCOL_VERSION = 0b0001
DEFAULT_HEADER_SIZE = 0b0001

# Message Types
FULL_CLIENT_REQUEST = 0b0001
AUDIO_ONLY_REQUEST = 0b0010
FULL_SERVER_RESPONSE = 0b1001
SERVER_ACK = 0b1011
SERVER_ERROR_RESPONSE = 0b1111

# Message Type Specific Flags
NO_SEQUENCE = 0b0000
POS_SEQUENCE = 0b0001
NEG_SEQUENCE = 0b0010
NEG_WITH_SEQUENCE = 0b0011

# Message Serialization
NO_SERIALIZATION = 0b0000
JSON_SERIALIZATION = 0b0001

# Message Compression
NO_COMPRESSION = 0b0000
GZIP_COMPRESSION = 0b0001


class DoubaoV3StreamingClient:
    """单个客户端的流式ASR连接"""
    
    def __init__(self, client_id: str, config: dict, result_callback: Callable[[str, str, dict], None]):
        self.client_id = client_id
        self.config = config
        self.result_callback = result_callback
        
        # WebSocket connection
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.connected = False
        self.connecting = False
        
        # Protocol state
        self.sequence = 0
        self.session_started = False
        
        # Connection parameters from config - 使用ASR.DoubaoASR配置
        doubao_asr_config = config.get("ASR", {}).get("DoubaoASR", {})
        self.app_id = doubao_asr_config.get("appid", "")
        self.access_token = doubao_asr_config.get("access_token", "")
        self.resource_id = "volc.bigasr.sauc.duration"  # V3版本固定使用这个resource_id
        
        # Lock for thread safety
        self._lock = threading.Lock()
        
        logger.bind(tag=TAG).info(f"🎤 创建DoubaoV3流式客户端: {client_id}")
    
    async def connect(self) -> bool:
        """建立WebSocket连接"""
        if self.connected:
            return True
            
        if self.connecting:
            # 等待其他线程完成连接
            for _ in range(50):  # 最多等待5秒
                if self.connected:
                    return True
                await asyncio.sleep(0.1)
            return False
            
        with self._lock:
            if self.connected:
                return True
            self.connecting = True
        
        try:
            url = "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel"
            headers = {
                "X-Api-App-Key": self.app_id,
                "X-Api-Access-Key": self.access_token,
                "X-Api-Resource-Id": self.resource_id,
                "X-Api-Connect-Id": str(uuid.uuid4())
            }
            
            logger.bind(tag=TAG).info(f"🎤 [{self.client_id}] 连接Doubao ASR: {url}")
            
            self.websocket = await websockets.connect(url, additional_headers=headers, ping_interval=30)
            
            # 发送初始化请求
            if await self._send_init_request():
                self.connected = True
                self.session_started = True
                
                # 启动消息接收协程
                asyncio.create_task(self._receive_messages())
                
                logger.bind(tag=TAG).info(f"🎤 [{self.client_id}] Doubao ASR连接建立成功")
                return True
            else:
                logger.bind(tag=TAG).error(f"🎤 [{self.client_id}] 初始化请求失败")
                await self._cleanup_connection()
                return False
                
        except Exception as e:
            logger.bind(tag=TAG).error(f"🎤 [{self.client_id}] 连接失败: {e}")
            await self._cleanup_connection()
            return False
        finally:
            self.connecting = False
    
    async def _send_init_request(self) -> bool:
        """发送初始化请求"""
        try:
            # 构建payload
            payload = {
                "user": {"uid": self.client_id},
                "audio": {
                    "format": "pcm",
                    "sample_rate": 16000,
                    "bits": 16,
                    "channel": 1,
                    "codec": "raw"
                },
                "request": {
                    "model_name": "bigmodel",
                    "enable_punc": True
                }
            }
            
            # 序列化和压缩
            payload_str = json.dumps(payload)
            payload_bytes = gzip.compress(payload_str.encode('utf-8'))
            
            # 构建完整请求
            header = self._build_header(FULL_CLIENT_REQUEST, POS_SEQUENCE, JSON_SERIALIZATION, GZIP_COMPRESSION)
            self.sequence = 1
            sequence_bytes = struct.pack('>I', self.sequence)
            payload_size = struct.pack('>I', len(payload_bytes))
            
            full_request = header + sequence_bytes + payload_size + payload_bytes
            
            await self.websocket.send(full_request)
            logger.bind(tag=TAG).debug(f"🎤 [{self.client_id}] 发送初始化请求，序列号: {self.sequence}")
            
            return True
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"🎤 [{self.client_id}] 发送初始化请求失败: {e}")
            return False
    
    def _build_header(self, message_type: int, flags: int, serialization: int, compression: int) -> bytes:
        """构建协议头"""
        header = bytearray(4)
        header[0] = (PROTOCOL_VERSION << 4) | DEFAULT_HEADER_SIZE
        header[1] = (message_type << 4) | flags
        header[2] = (serialization << 4) | compression
        header[3] = 0  # reserved
        return bytes(header)
    
    async def send_audio(self, audio_data: bytes, is_last: bool = False) -> bool:
        """发送音频数据"""
        if not self.connected or not self.websocket:
            logger.bind(tag=TAG).warning(f"🎤 [{self.client_id}] 连接未建立，忽略音频数据")
            return False
        
        try:
            self.sequence += 1
            if is_last:
                self.sequence = -self.sequence
            
            # 构建音频请求
            flags = NEG_WITH_SEQUENCE if is_last else POS_SEQUENCE
            header = self._build_header(AUDIO_ONLY_REQUEST, flags, JSON_SERIALIZATION, GZIP_COMPRESSION)
            sequence_bytes = struct.pack('>i', self.sequence)  # signed int for negative sequence
            
            # 压缩音频数据
            compressed_audio = gzip.compress(audio_data)
            payload_size = struct.pack('>I', len(compressed_audio))
            
            audio_request = header + sequence_bytes + payload_size + compressed_audio
            
            await self.websocket.send(audio_request)
            
            if is_last:
                logger.bind(tag=TAG).debug(f"🎤 [{self.client_id}] 发送最后音频包，序列号: {self.sequence}")
            
            return True
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"🎤 [{self.client_id}] 发送音频失败: {e}")
            return False
    
    async def _receive_messages(self):
        """接收服务器消息"""
        try:
            async for message in self.websocket:
                if isinstance(message, bytes):
                    await self._handle_binary_message(message)
                else:
                    logger.bind(tag=TAG).debug(f"🎤 [{self.client_id}] 收到文本消息: {message}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.bind(tag=TAG).info(f"🎤 [{self.client_id}] 服务器关闭连接")
        except Exception as e:
            logger.bind(tag=TAG).error(f"🎤 [{self.client_id}] 消息接收异常: {e}")
        finally:
            await self._cleanup_connection()
    
    async def _handle_binary_message(self, message: bytes):
        """处理二进制消息"""
        try:
            if len(message) < 4:
                logger.bind(tag=TAG).warning(f"🎤 [{self.client_id}] 消息太短，忽略")
                return
            
            # 解析协议头
            header = message[:4]
            protocol_version = (header[0] >> 4) & 0x0F
            header_size = header[0] & 0x0F
            message_type = (header[1] >> 4) & 0x0F
            flags = header[1] & 0x0F
            serialization = (header[2] >> 4) & 0x0F
            compression = header[2] & 0x0F
            
            # 解析序列号
            if len(message) < 8:
                return
            sequence = struct.unpack('>i', message[4:8])[0]
            
            # 解析payload
            if len(message) < 12:
                return
            payload_size = struct.unpack('>I', message[8:12])[0]
            payload = message[12:]
            
            # 处理不同类型的消息
            if message_type == FULL_SERVER_RESPONSE:
                await self._handle_server_response(payload, compression, serialization)
            elif message_type == SERVER_ACK:
                logger.bind(tag=TAG).debug(f"🎤 [{self.client_id}] 收到服务器确认，序列号: {sequence}")
            elif message_type == SERVER_ERROR_RESPONSE:
                error_code = sequence  # 在错误响应中，序列号字段表示错误码
                error_msg = payload.decode('utf-8') if payload else "未知错误"
                logger.bind(tag=TAG).error(f"🎤 [{self.client_id}] 服务器错误 {error_code}: {error_msg}")
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"🎤 [{self.client_id}] 处理二进制消息失败: {e}")
    
    async def _handle_server_response(self, payload: bytes, compression: int, serialization: int):
        """处理服务器响应"""
        try:
            if len(payload) == 0:
                logger.bind(tag=TAG).debug(f"🎤 [{self.client_id}] 收到空响应，跳过处理")
                return
            
            # 解压缩
            if compression == GZIP_COMPRESSION:
                try:
                    payload = gzip.decompress(payload)
                except Exception as e:
                    logger.bind(tag=TAG).warning(f"🎤 [{self.client_id}] GZIP解压失败: {e}")
                    return
            
            # 反序列化
            if serialization == JSON_SERIALIZATION:
                try:
                    payload_str = payload.decode('utf-8')
                    logger.bind(tag=TAG).debug(f"🎤 [{self.client_id}] 收到响应: {payload_str}")
                    response = json.loads(payload_str)
                    
                    # 检查响应结构
                    if 'code' in response:
                        code = response.get('code')
                        if code != 1000:  # 1000表示成功
                            logger.bind(tag=TAG).warning(f"🎤 [{self.client_id}] 服务器返回错误码: {code}, 消息: {response.get('message', '未知错误')}")
                            return
                    
                    # 提取识别结果 - 兼容不同的响应格式
                    text = ""
                    if 'result' in response:
                        result = response['result']
                        if isinstance(result, dict):
                            # Doubao V3格式: {"result": {"text": "..."}}
                            text = result.get('text', '')
                        elif isinstance(result, list) and len(result) > 0:
                            # 其他格式: {"result": [{"text": "..."}]}
                            text = result[0].get('text', '')
                    
                    if text:
                        # 调用回调函数，传递完整响应
                        if self.result_callback:
                            self.result_callback(self.client_id, text, response)
                        
                        logger.bind(tag=TAG).info(f"🎤 [{self.client_id}] 流式ASR识别结果: {text}")
                    else:
                        # 即使是空文本，也需要调用回调来更新VAD状态
                        if self.result_callback:
                            self.result_callback(self.client_id, "", response)
                        
                        # 空文本结果，记录调试信息
                        # logger.bind(tag=TAG).debug(f"🎤 [{self.client_id}] 收到空识别结果")
                    
                except json.JSONDecodeError as e:
                    logger.bind(tag=TAG).warning(f"🎤 [{self.client_id}] JSON解析失败: {e}")
                except UnicodeDecodeError as e:
                    logger.bind(tag=TAG).warning(f"🎤 [{self.client_id}] UTF-8解码失败: {e}")
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"🎤 [{self.client_id}] 处理服务器响应失败: {e}")
            import traceback
            logger.bind(tag=TAG).debug(f"🎤 [{self.client_id}] 详细错误: {traceback.format_exc()}")
    
    async def close(self):
        """关闭连接"""
        logger.bind(tag=TAG).info(f"🎤 [{self.client_id}] 关闭流式ASR连接")
        await self._cleanup_connection()
    
    async def _cleanup_connection(self):
        """清理连接资源"""
        self.connected = False
        self.session_started = False
        self.connecting = False
        
        if self.websocket:
            try:
                await self.websocket.close()
            except:
                pass
            self.websocket = None


class DoubaoV3StreamingManager:
    """流式ASR连接管理器（单例模式）"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __init__(self):
        self.connections: Dict[str, DoubaoV3StreamingClient] = {}
        self.connection_lock = threading.Lock()
        
    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    def _default_result_callback(self, client_id: str, text: str, asr_response: dict):
        """默认结果回调函数"""
        logger.bind(tag=TAG).info(f"🎤📝 [流式ASR结果] 客户端:{client_id} | 识别文本:{text}")
    
    async def get_connection(self, client_id: str, config: dict, result_callback: Optional[Callable[[str, str, dict], None]] = None) -> Optional[DoubaoV3StreamingClient]:
        """获取或创建客户端连接"""
        with self.connection_lock:
            if client_id in self.connections:
                client = self.connections[client_id]
                if client.connected:
                    return client
                elif client.connecting:
                    # 如果正在连接中，返回None避免重复创建
                    logger.bind(tag=TAG).debug(f"🎤 [{client_id}] 连接正在建立中，跳过重复创建")
                    return None
                else:
                    # 连接已断开，删除旧连接
                    del self.connections[client_id]
        
        # 创建新连接前再次检查，防止并发创建
        with self.connection_lock:
            if client_id in self.connections:
                client = self.connections[client_id]
                if client.connected or client.connecting:
                    return client if client.connected else None
        
        # 创建新连接
        callback = result_callback or self._default_result_callback
        client = DoubaoV3StreamingClient(client_id, config, callback)
        
        # 先将连接加入管理器（处于connecting状态），防止重复创建
        with self.connection_lock:
            self.connections[client_id] = client
        
        if await client.connect():
            return client
        else:
            # 连接失败，从管理器中移除
            with self.connection_lock:
                if client_id in self.connections and self.connections[client_id] == client:
                    del self.connections[client_id]
            return None
    
    async def remove_connection(self, client_id: str):
        """移除并关闭客户端连接"""
        with self.connection_lock:
            if client_id in self.connections:
                client = self.connections[client_id]
                del self.connections[client_id]
                
                # 异步关闭连接
                try:
                    await client.close()
                    logger.bind(tag=TAG).info(f"🎤 已清理客户端 {client_id} 的流式ASR连接")
                except Exception as e:
                    logger.bind(tag=TAG).error(f"🎤 清理客户端 {client_id} 连接时出错: {e}")
    
    def get_connection_count(self) -> int:
        """获取当前连接数"""
        with self.connection_lock:
            return len(self.connections)
    
    def list_clients(self) -> list:
        """列出所有活跃客户端"""
        with self.connection_lock:
            return list(self.connections.keys())


# 全局管理器实例
streaming_manager = DoubaoV3StreamingManager.get_instance()


# 添加连接获取频率限制
_connection_request_times = {}
_connection_lock = threading.Lock()

async def process_streaming_asr(conn, audio_data: bytes):
    """处理流式ASR的主函数"""
    try:
        if not hasattr(conn, 'client_id') or not conn.client_id:
            return
        
        # 频率限制：同一客户端每100ms内只能请求一次连接
        current_time = time.time()
        with _connection_lock:
            last_request_time = _connection_request_times.get(conn.client_id, 0)
            if current_time - last_request_time < 0.1:  # 100ms
                # 直接尝试使用已有连接发送音频，不再尝试创建新连接
                with streaming_manager.connection_lock:
                    if conn.client_id in streaming_manager.connections:
                        client = streaming_manager.connections[conn.client_id]
                        if client.connected:
                            await _send_audio_to_client(conn, client, audio_data)
                return
            _connection_request_times[conn.client_id] = current_time
        
        # 创建回调函数，将ASR结果传递给StreamVADManager
        def stream_vad_callback(client_id: str, text: str, asr_response: dict):
            """将ASR结果传递给StreamVADManager的回调函数"""
            try:
                # 获取StreamVADManager并传递结果
                if hasattr(conn, 'stream_vad_manager') and conn.stream_vad_manager:
                    conn.stream_vad_manager.on_asr_result(client_id, text, asr_response)
            except Exception as e:
                logger.bind(tag=TAG).error(f"🎤 StreamVAD回调处理异常: {e}")
        
        # 获取连接
        client = await streaming_manager.get_connection(conn.client_id, conn.config, stream_vad_callback)
        if not client:
            # 减少警告日志频率
            if current_time - _connection_request_times.get(f"{conn.client_id}_warning", 0) > 5:  # 5秒警告一次
                logger.bind(tag=TAG).warning(f"🎤 [{conn.client_id}] 无法获取流式ASR连接")
                _connection_request_times[f"{conn.client_id}_warning"] = current_time
            return
        
        await _send_audio_to_client(conn, client, audio_data)
    
    except Exception as e:
        logger.bind(tag=TAG).error(f"🎤 [{conn.client_id}] 流式ASR处理异常: {e}")


async def _send_audio_to_client(conn, client, audio_data: bytes):
    """发送音频数据到ASR客户端"""
    try:
        # 将Opus音频解码为PCM
        if hasattr(conn, 'asr') and hasattr(conn.asr, 'decode_opus'):
            # 使用现有ASR的解码方法
            pcm_frames = conn.asr.decode_opus([audio_data])
            pcm_data = b''.join(pcm_frames)
        else:
            # 如果没有可用的解码器，直接使用音频数据
            pcm_data = audio_data
        
        # 发送音频数据到流式ASR
        await client.send_audio(pcm_data, is_last=False)
        
    except Exception as e:
        logger.bind(tag=TAG).debug(f"🎤 [{conn.client_id}] 音频发送失败: {e}")


async def cleanup_client_streaming_asr(client_id: str):
    """清理指定客户端的流式ASR连接"""
    if client_id:
        await streaming_manager.remove_connection(client_id)