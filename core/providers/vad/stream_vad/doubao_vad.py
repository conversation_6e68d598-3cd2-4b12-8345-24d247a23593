import time
import re
from typing import Dict
from .base import StreamVADProviderBase
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()


class DoubaoStreamVAD(StreamVADProviderBase):
    """基于Doubao ASR文本流的VAD实现"""
    
    def __init__(self, config: dict):
        self.config = config
        
        # 从配置中获取静默阈值，默认为1000ms
        stream_vad_config = config.get("StreamVAD", {})
        self.silence_duration_ms = stream_vad_config.get("silence_duration_ms", 1000)
        # 句尾标点后的确认静默时间，默认为500ms（较短）
        self.punctuation_confirm_ms = stream_vad_config.get("punctuation_confirm_ms", 500)
        
        # 计时器相关
        self.silence_start_time = None
        self.last_text = ""
        self.has_end_punctuation = False  # 标记是否检测到句尾标点
        
        # 句尾标点符号
        self.sentence_end_punctuation = {"。", "？", "！", ".", "?", "!"}
        
        logger.bind(tag=TAG).info(f"🎯 创建DoubaoStreamVAD，静默阈值: {self.silence_duration_ms}ms, 标点确认阈值: {self.punctuation_confirm_ms}ms")
    
    def is_speech_complete(self, asr_response: Dict) -> bool:
        """
        判断一句话是否结束。
        
        逻辑优先级：
        1. 如果有句尾标点，则检查静默超时或definite标志
        2. 如果没有句尾标点，仅依赖静默超时
        3. definite标志作为辅助条件，不作为强依赖
        
        Args:
            asr_response: 完整的ASR响应字典
            
        Returns:
            bool: 如果一句话结束返回True，否则返回False
        """
        try:
            # 解析ASR响应
            result = asr_response.get("result", {})
            text = result.get("text", "").strip()
            
            current_time = time.time()
            
            logger.bind(tag=TAG).debug(f"🎯 完整ASR响应处理中，文本: '{text}'")
            
            # 检查definite标志（作为辅助条件）
            # utterances可能在result下或顶层
            utterances = asr_response.get("utterances", [])
            if not utterances and "result" in asr_response:
                utterances = asr_response["result"].get("utterances", [])
            
            logger.bind(tag=TAG).debug(f"🎯 提取到的utterances数量: {len(utterances)}")
            
            definite = False
            if utterances and len(utterances) > 0:
                # 查找与当前文本匹配的utterance，或者有内容的utterance
                for utterance in utterances:
                    utterance_text = utterance.get("text", "").strip()
                    if utterance_text == text.strip() or (utterance_text and not text):
                        definite = utterance.get("definite", False)
                        logger.bind(tag=TAG).debug(f"🎯 找到匹配的utterance: text='{utterance_text}', definite={definite}")
                        break
                else:
                    # 如果没有找到匹配的，使用第一个有文本的utterance
                    for utterance in utterances:
                        if utterance.get("text", "").strip():
                            definite = utterance.get("definite", False)
                            logger.bind(tag=TAG).debug(f"🎯 使用第一个有文本的utterance: definite={definite}")
                            break
            
            # 处理文本为空的情况：静默计时
            if not text:
                if self.silence_start_time is None:
                    self.silence_start_time = current_time
                    logger.bind(tag=TAG).debug(f"🎯 开始静默计时")
                else:
                    silence_duration = (current_time - self.silence_start_time) * 1000
                    # 根据是否有句尾标点选择不同的阈值
                    threshold = self.punctuation_confirm_ms if self.has_end_punctuation else self.silence_duration_ms
                    
                    if silence_duration >= threshold:
                        threshold_type = "标点确认" if self.has_end_punctuation else "常规静默"
                        logger.bind(tag=TAG).debug(f"🎯 {threshold_type}时间达到阈值 {silence_duration:.0f}ms，判断为语音结束")
                        self.reset()
                        return True
                return False
            
            # 文本非空：重置静默计时器，更新最后文本
            self.silence_start_time = None
            self.last_text = text
            logger.bind(tag=TAG).debug(f"🎯 收到文本: '{text}'")
            
            # 检查是否有句尾标点
            current_has_punctuation = text and text[-1] in self.sentence_end_punctuation
            
            if current_has_punctuation:
                # 检测到句尾标点：立即检查definite标志
                if definite:
                    logger.bind(tag=TAG).debug(f"🎯 检测到句尾标点 '{text[-1]}' + definite=True，立即判断为语音结束")
                    self.reset()
                    return True
                else:
                    # 有句尾标点但definite=False，标记状态等待静默确认
                    self.has_end_punctuation = True
                    logger.bind(tag=TAG).debug(f"🎯 检测到句尾标点 '{text[-1]}'，标记等待静默确认")
            else:
                # 没有句尾标点：清除标点标记，仅在definite=True时结束
                self.has_end_punctuation = False
                if definite:
                    logger.bind(tag=TAG).debug(f"🎯 没有句尾标点但检测到definite=True，判断为语音结束")
                    self.reset()
                    return True
            
            return False
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"🎯 VAD判断异常: {e}")
            return False
    
    def reset(self):
        """重置VAD状态"""
        self.silence_start_time = None
        self.last_text = ""
        self.has_end_punctuation = False
        logger.bind(tag=TAG).debug(f"🎯 重置VAD状态")