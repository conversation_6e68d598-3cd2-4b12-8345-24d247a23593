from abc import ABC, abstractmethod
from typing import Dict


class StreamVADProviderBase(ABC):
    """流式VAD提供者基类"""
    
    @abstractmethod
    def is_speech_complete(self, asr_response: Dict) -> bool:
        """
        判断一句话是否结束。
        
        Args:
            asr_response: 完整的ASR响应字典
            
        Returns:
            bool: 如果一句话结束返回True，否则返回False
        """
        pass

    @abstractmethod
    def reset(self):
        """重置VAD状态"""
        pass