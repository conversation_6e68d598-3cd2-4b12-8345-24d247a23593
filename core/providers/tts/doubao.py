
import os
import uuid
import json
import base64
import aiohttp
from datetime import datetime
from typing import Dict
from core.utils.util import check_model_key
from core.providers.tts.base import TTSProviderBase
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()

class TTSProvider(TTSProviderBase):
    def __init__(self, config, delete_audio_file):
        super().__init__(config, delete_audio_file)
        self.appid = str(config.get("appid"))
        self.access_key = str(config.get("access_key", config.get("access_token")))
        self.api_app_key = "aGjiRDfUWi"  # Fixed value from official demo

        self.voice = config.get("voice")
        
        # 根据声音ID动态设置resource_id
        if self.voice and self.voice.startswith("S_"):
            self.resource_id = "volc.megatts.default"
        else:
            self.resource_id = "volc.service_type.10029"
        self.speed_ratio = float(config.get("speed_ratio", 1.0))
        self.volume_ratio = float(config.get("volume_ratio", 1.0))
        self.pitch_ratio = float(config.get("pitch_ratio", 1.0))
        self.request_timeout = float(config.get("request_timeout", 10.0))
        self.api_url = "https://openspeech.bytedance.com/api/v3/tts/unidirectional"

        check_model_key("TTS AppID", self.appid)
        check_model_key("TTS Access Key", self.access_key)

    def generate_filename(self, extension=".mp3"):
        return os.path.join(
            self.output_file,
            f"tts-{datetime.now().date()}@{uuid.uuid4().hex}{extension}",
        )

    async def text_to_speak(self, text, output_file, client_id: str = None, connection=None):
        voice_config = {
            "voice": self.voice,
            "speed_ratio": self.speed_ratio,
            "volume_ratio": self.volume_ratio,
            "pitch_ratio": self.pitch_ratio,
        }

        # 通过user_profile_manager获取用户配置（推荐方式）
        if connection and hasattr(connection, 'user_profile_manager'):
            user_voice = connection.user_profile_manager.get_voice()
            if user_voice:
                voice_config["voice"] = user_voice
                
                # Use default TTS parameters since advanced settings are not currently implemented
                # in the centralized voice configuration system
                voice_config.update({
                    "speed_ratio": self.speed_ratio,
                    "volume_ratio": self.volume_ratio,
                    "pitch_ratio": self.pitch_ratio,
                })

        # 根据实际使用的声音ID动态设置resource_id
        current_voice = voice_config["voice"]
        if current_voice and current_voice.startswith("S_"):
            resource_id = "volc.megatts.default"
        else:
            resource_id = "volc.service_type.10029"

        headers = {
            "X-Api-App-Id": self.appid,
            "X-Api-Access-Key": self.access_key,
            "X-Api-Resource-Id": resource_id,
            "X-Api-App-Key": self.api_app_key,
            "Content-Type": "application/json",
        }

        additions = {
            "disable_markdown_filter": True,
            "enable_language_detector": True,
            "enable_latex_tn": True,
            "disable_default_bit_rate": True,
            "max_length_to_filter_parenthesis": 0,
            "cache_config": {
                "text_type": 1,
                "use_cache": True
            }
        }

        payload = {
            "user": {"uid": client_id or "yuyan_server_user"},
            "req_params": {
                "text": text,
                "speaker": voice_config["voice"],
                "additions": json.dumps(additions),
                "audio_params": {
                    "format": "mp3",
                    "sample_rate": 24000,
                    "speed": voice_config["speed_ratio"],
                    "volume": voice_config["volume_ratio"],
                    "pitch": voice_config["pitch_ratio"],
                }
            }
        }

        try:
            logger.bind(tag=TAG).debug(f"开始Doubao TTS请求 - 文本: {text[:50]}..., 声音: {voice_config['voice']}")
            logger.bind(tag=TAG).debug(f"API URL: {self.api_url}")
            logger.bind(tag=TAG).debug(f"请求头: {headers}")
            logger.bind(tag=TAG).debug(f"请求体: {json.dumps(payload, ensure_ascii=False)}")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.api_url, headers=headers, json=payload, timeout=self.request_timeout) as response:
                    logger.bind(tag=TAG).debug(f"API响应状态: {response.status}")
                    logger.bind(tag=TAG).debug(f"API响应头: {dict(response.headers)}")
                    
                    response.raise_for_status()
                    
                    bytes_written = 0
                    chunk_count = 0
                    
                    with open(output_file, "wb") as f:
                        async for line in response.content:
                            if not line:
                                continue
                            
                            chunk_count += 1
                            # logger.bind(tag=TAG).debug(f"处理第{chunk_count}个数据块，长度: {len(line)}")
                            
                            try:
                                data = json.loads(line.decode('utf-8'))
                                # logger.bind(tag=TAG).debug(f"解析JSON成功: code={data.get('code')}, 有数据={bool(data.get('data'))}")
                                
                                if data.get("code", 0) == 0 and "data" in data and data["data"]:
                                    audio_chunk = base64.b64decode(data["data"])
                                    f.write(audio_chunk)
                                    bytes_written += len(audio_chunk)
                                    # logger.bind(tag=TAG).debug(f"写入音频数据: {len(audio_chunk)} 字节，累计: {bytes_written} 字节")
                                elif data.get("code", 0) == 20000000: # End of stream
                                    logger.bind(tag=TAG).debug("收到流结束信号")
                                    break
                                elif data.get("code", 0) > 0:
                                    logger.bind(tag=TAG).error(f"Doubao TTS API error: {data}")
                                    raise Exception(f"Doubao TTS API error: {data.get('message', 'Unknown error')}")
                            except (json.JSONDecodeError, UnicodeDecodeError) as e:
                                logger.bind(tag=TAG).warning(f"Failed to decode JSON chunk from Doubao TTS: {e} - Chunk: {line}")
                                continue
                    
                    logger.bind(tag=TAG).info(f"TTS完成 - 文本: [{text[:30]}...], 声音: {voice_config['voice']}, 文件: {output_file}, 总字节: {bytes_written}, 处理块数: {chunk_count}")
                    
                    if bytes_written == 0:
                        logger.bind(tag=TAG).error(f"TTS文件为空！未写入任何音频数据 - 文本: {text}")
                        return False
                    
            return True
        except aiohttp.ClientError as e:
            logger.bind(tag=TAG).error(f"TTS网络请求失败: {e}")
            if 'response' in locals() and hasattr(response, 'text'):
                try:
                    error_body = await response.text()
                    logger.bind(tag=TAG).error(f"错误响应体: {error_body}")
                except Exception as read_e:
                    logger.bind(tag=TAG).error(f"读取错误响应体失败: {read_e}")
            raise
        except Exception as e:
            logger.bind(tag=TAG).error(f"TTS处理异常: {e}")
            logger.bind(tag=TAG).error(f"异常类型: {type(e).__name__}")
            import traceback
            logger.bind(tag=TAG).error(f"异常堆栈: {traceback.format_exc()}")
            raise
