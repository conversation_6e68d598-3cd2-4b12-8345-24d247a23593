import asyncio
from config.logger import setup_logging
import os
from abc import ABC, abstractmethod
from core.utils.tts import MarkdownCleaner
from core.utils.util import audio_to_data

TAG = __name__
logger = setup_logging()


class TTSProviderBase(ABC):
    def __init__(self, config, delete_audio_file):
        self.delete_audio_file = delete_audio_file
        self.output_file = config.get("output_dir")
        # 获取请求超时时间配置，默认7秒
        self.request_timeout = float(config.get("request_timeout", 7.0))

    @abstractmethod
    def generate_filename(self):
        pass

    def to_tts(self, text, client_id=None, connection=None):
        tmp_file = self.generate_filename()
        try:
            max_repeat_time = 3  # 减少重试次数，避免长时间阻塞
            original_text = text  # 保存原文用于日志
            text = MarkdownCleaner.clean_markdown(text)
            
            # 检查清理后的文本是否为空
            if not text or not text.strip():
                logger.bind(tag=TAG).info(f"文本清理后为空，跳过TTS处理 - 原文: {original_text.strip()}")
                return None
            
            while not os.path.exists(tmp_file) and max_repeat_time > 0:
                try:
                    # 使用asyncio.wait_for添加超时控制
                    try:
                        # 检查是否已有运行的事件循环
                        loop = asyncio.get_running_loop()
                        # 如果有，在新线程中运行
                        import concurrent.futures
                        import threading
                        
                        def run_in_thread():
                            return asyncio.run(asyncio.wait_for(
                                self.text_to_speak(text, tmp_file, client_id, connection), 
                                timeout=self.request_timeout + 1.0
                            ))
                        
                        logger.bind(tag=TAG).info(f"调用TTS朗读文本: {text}")
                        with concurrent.futures.ThreadPoolExecutor() as executor:
                            future = executor.submit(run_in_thread)
                            result = future.result()
                            
                            # 检查TTS方法的返回值
                            if result is False:
                                logger.bind(tag=TAG).error(f"TTS方法返回False - 文本: {text}")
                                max_repeat_time -= 1
                                continue
                                
                    except RuntimeError:
                        # 没有运行的事件循环，直接使用asyncio.run
                        logger.bind(tag=TAG).debug("使用asyncio.run运行TTS")
                        result = asyncio.run(asyncio.wait_for(
                            self.text_to_speak(text, tmp_file, client_id, connection), 
                            timeout=self.request_timeout + 1.0
                        ))
                        
                        # 检查TTS方法的返回值
                        if result is False:
                            logger.bind(tag=TAG).error(f"TTS方法返回False - 文本: {text}")
                            max_repeat_time -= 1
                            continue
                            
                    # 验证文件是否实际创建且有内容
                    if os.path.exists(tmp_file):
                        file_size = os.path.getsize(tmp_file)
                        if file_size == 0:
                            logger.bind(tag=TAG).warning(f"TTS文件为空，删除并重试 - 文件: {tmp_file}")
                            os.remove(tmp_file)
                            max_repeat_time -= 1
                            continue
                        else:
                            logger.bind(tag=TAG).debug(f"TTS文件生成成功，大小: {file_size} 字节")
                            
                except asyncio.TimeoutError:
                    logger.bind(tag=TAG).warning(
                        f"TTS请求超时{3 - max_repeat_time + 1}次: {text}"
                    )
                    # 清理可能的临时文件
                    if os.path.exists(tmp_file):
                        os.remove(tmp_file)
                    max_repeat_time -= 1
                except Exception as e:
                    logger.bind(tag=TAG).warning(
                        f"语音生成失败{3 - max_repeat_time + 1}次: {text}，错误: {e}"
                    )
                    logger.bind(tag=TAG).debug(f"异常类型: {type(e).__name__}")
                    # 未执行成功，删除文件
                    if os.path.exists(tmp_file):
                        os.remove(tmp_file)
                    max_repeat_time -= 1

            if max_repeat_time > 0:
                logger.bind(tag=TAG).info(
                    f"语音生成成功: {text}:{tmp_file}，重试{3 - max_repeat_time}次"
                )
            else:
                logger.bind(tag=TAG).error(
                    f"语音生成失败: {text}，请检查网络或TTS服务是否正常"
                )

            return tmp_file
        except Exception as e:
            logger.bind(tag=TAG).error(f"TTS生成异常: {e}")
            # 清理可能的临时文件
            if tmp_file and os.path.exists(tmp_file):
                try:
                    os.remove(tmp_file)
                except:
                    pass
            return None

    @abstractmethod
    async def text_to_speak(self, text, output_file, client_id=None, connection=None):
        pass

    def audio_to_pcm_data(self, audio_file_path):
        """音频文件转换为PCM编码"""
        return audio_to_data(audio_file_path, is_opus=False)

    def audio_to_opus_data(self, audio_file_path):
        """音频文件转换为Opus编码"""
        return audio_to_data(audio_file_path, is_opus=True)
