import json
import asyncio
import traceback
from plugins_func.register import Action, ActionResponse

TAG = __name__

def _handle_mcp_tool_call(self, function_call_data):
    function_arguments = function_call_data["arguments"]
    function_name = function_call_data["name"]
    try:
        args_dict = function_arguments
        if isinstance(function_arguments, str):
            try:
                args_dict = json.loads(function_arguments)
            except json.JSONDecodeError:
                self.logger.bind(tag=TAG).error(
                    f"无法解析 function_arguments: {function_arguments}"
                )
                return ActionResponse(
                    action=Action.REQLLM, result="参数解析失败", response=""
                )
        # 添加超时处理，避免无限等待
        try:
            self.logger.bind(tag=TAG).debug(f"开始执行MCP工具: {function_name}, 参数: {args_dict}")
            tool_result = asyncio.run_coroutine_threadsafe(
                self.mcp_manager.execute_tool(function_name, args_dict), self.loop
            ).result(timeout=30)  # 30秒超时
            self.logger.bind(tag=TAG).debug(f"MCP工具执行完成: {function_name}, 结果类型: {type(tool_result)}")
            self.logger.bind(tag=TAG).debug(f"MCP工具执行结果: {repr(tool_result)}")
        except asyncio.TimeoutError:
            self.logger.bind(tag=TAG).error(f"MCP工具调用超时: {function_name}")
            return ActionResponse(
                action=Action.REQLLM, result="搜索服务响应超时，请稍后再试", response=""
            )
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"MCP工具调用异常: {function_name}, 错误: {e}")
            # traceback already imported at top
            self.logger.bind(tag=TAG).error(f"MCP工具调用异常堆栈: {traceback.format_exc()}")
            return ActionResponse(
                action=Action.REQLLM, result="搜索服务暂时不可用，请稍后再试", response=""
            )
        # meta=None content=[TextContent(type='text', text='北京当前天气:\n温度: 21°C\n天气: 晴\n湿度: 6%\n风向: 西北 风\n风力等级: 5级', annotations=None)] isError=False
        content_text = ""
        self.logger.bind(tag=TAG).debug(f"检查MCP工具结果: tool_result is None: {tool_result is None}")
        if tool_result is not None:
            self.logger.bind(tag=TAG).debug(f"MCP工具结果有内容: tool_result.content is None: {tool_result.content is None}")
            if tool_result.content is not None:
                self.logger.bind(tag=TAG).debug(f"MCP工具内容数量: {len(tool_result.content)}")
                for i, content in enumerate(tool_result.content):
                    content_type = content.type
                    self.logger.bind(tag=TAG).debug(f"内容 {i}: 类型={content_type}")
                    if content_type == "text":
                        # 安全处理content.text，确保不为None
                        text_content = content.text if content.text is not None else ""
                        self.logger.bind(tag=TAG).debug(f"内容 {i}: 文本长度={len(text_content)}")
                        self.logger.bind(tag=TAG).debug(f"内容 {i}: 文本预览={text_content[:200]}...")
                        content_text += text_content
                    elif content_type == "image":
                        self.logger.bind(tag=TAG).debug(f"内容 {i}: 图片类型，跳过")
            else:
                self.logger.bind(tag=TAG).warning("MCP工具返回结果但content为None")
        else:
            self.logger.bind(tag=TAG).warning("MCP工具返回None结果")
        # 安全检查content_text是否为None或空
        self.logger.bind(tag=TAG).debug(f"🔧 检查content_text: type={type(content_text)}, is_none={content_text is None}")
        if content_text is not None:
            try:
                content_text_len = len(content_text)
                self.logger.bind(tag=TAG).debug(f"🔧 最终内容文本长度: {content_text_len}")
                self.logger.bind(tag=TAG).debug(f"🔧 最终内容文本前200字符: {str(content_text)[:200]}...")
            except Exception as len_error:
                self.logger.bind(tag=TAG).error(f"🔧 计算content_text长度时出错: {len_error}")
                self.logger.bind(tag=TAG).error(f"🔧 content_text类型: {type(content_text)}")
                self.logger.bind(tag=TAG).error(f"🔧 content_text的repr: {repr(content_text)}")
                return ActionResponse(
                    action=Action.REQLLM, result="内容长度计算失败", response=""
                )
        else:
            self.logger.bind(tag=TAG).warning("🔧 content_text为None")
            return ActionResponse(
                action=Action.REQLLM, result="搜索没有找到相关结果", response=""
            )
        if content_text is not None and len(str(content_text)) > 0:
            # 清理MCP工具返回的内容，避免过长
            self.logger.bind(tag=TAG).debug(f"🔧 开始清理MCP内容，原始长度: {len(content_text)}")
            cleaned_content = _clean_mcp_content(self, content_text, function_name)
            self.logger.bind(tag=TAG).debug(f"🔧 清理后内容长度: {len(cleaned_content)}")
            self.logger.bind(tag=TAG).debug(f"🔧 清理后内容预览: {cleaned_content[:200]}...")
            return ActionResponse(
                action=Action.REQLLM, result=cleaned_content, response=""
            )
        else:
            self.logger.bind(tag=TAG).warning("🔧 MCP工具返回空内容或None")
            return ActionResponse(
                action=Action.REQLLM, result="搜索没有找到相关结果", response=""
            )
    except Exception as e:
        self.logger.bind(tag=TAG).error(f"MCP工具调用错误: {e}")
        return ActionResponse(
            action=Action.REQLLM, result="工具调用出错", response=""
        )
    return ActionResponse(action=Action.REQLLM, result="工具调用出错", response="")

def _clean_mcp_content(self, content_text, function_name):
    """清理MCP工具返回的内容，避免过长"""
    try:
        self.logger.bind(tag=TAG).debug(f"🧹 _clean_mcp_content开始: function_name={function_name}")
        self.logger.bind(tag=TAG).debug(f"🧹 content_text类型: {type(content_text)}")
        self.logger.bind(tag=TAG).debug(f"🧹 content_text是否为None: {content_text is None}")
        
        if content_text is not None:
            try:
                content_length = len(content_text)
                self.logger.bind(tag=TAG).debug(f"🧹 content_text长度: {content_length}")
                self.logger.bind(tag=TAG).debug(f"🧹 content_text前200字符: {str(content_text)[:200]}...")
            except Exception as len_error:
                self.logger.bind(tag=TAG).error(f"🧹 计算content_text长度时出错: {len_error}")
                self.logger.bind(tag=TAG).error(f"🧹 content_text的repr: {repr(content_text)}")
                # 尝试转换为字符串
                try:
                    content_text = str(content_text)
                    self.logger.bind(tag=TAG).debug(f"🧹 转换为字符串后长度: {len(content_text)}")
                except Exception as str_error:
                    self.logger.bind(tag=TAG).error(f"🧹 转换为字符串失败: {str_error}")
                    return "内容类型转换失败"
        else:
            self.logger.bind(tag=TAG).warning(f"🧹 content_text为None，返回默认消息")
            return "MCP工具返回空内容"
        from core.mcp.content_cleaners import MCPCleanerFactory
        self.logger.bind(tag=TAG).debug(f"🧹 开始调用MCPCleanerFactory.clean_content")
        # 使用清理器工厂创建对应的清理器并清理内容
        result = MCPCleanerFactory.clean_content(function_name, content_text)
        self.logger.bind(tag=TAG).debug(f"🧹 MCPCleanerFactory.clean_content完成")
        self.logger.bind(tag=TAG).debug(f"🧹 清理结果类型: {type(result)}")
        self.logger.bind(tag=TAG).debug(f"🧹 清理结果是否为None: {result is None}")
        
        if result is not None:
            try:
                result_length = len(result)
                self.logger.bind(tag=TAG).debug(f"🧹 清理结果长度: {result_length}")
                self.logger.bind(tag=TAG).debug(f"🧹 清理结果前200字符: {str(result)[:200]}...")
            except Exception as result_len_error:
                self.logger.bind(tag=TAG).error(f"🧹 计算清理结果长度时出错: {result_len_error}")
                self.logger.bind(tag=TAG).error(f"🧹 清理结果的repr: {repr(result)}")
                # 尝试转换为字符串
                try:
                    result = str(result)
                    self.logger.bind(tag=TAG).debug(f"🧹 转换清理结果为字符串后长度: {len(result)}")
                except Exception as result_str_error:
                    self.logger.bind(tag=TAG).error(f"🧹 转换清理结果为字符串失败: {result_str_error}")
                    return "清理结果类型转换失败"
        else:
            self.logger.bind(tag=TAG).warning(f"🧹 清理结果为None，返回默认消息")
            return "内容清理后为空"
        return result
    except Exception as e:
        self.logger.bind(tag=TAG).error(f"🧹 _clean_mcp_content异常: {e}")
        self.logger.bind(tag=TAG).error(f"🧹 异常类型: {type(e).__name__}")
        # traceback already imported at top
        self.logger.bind(tag=TAG).error(f"🧹 异常堆栈: {traceback.format_exc()}")
        # 返回原始内容或安全的默认值
        if content_text is not None:
            try:
                return str(content_text)[:2000]  # 截断到安全长度
            except Exception as fallback_error:
                self.logger.bind(tag=TAG).error(f"🧹 fallback处理失败: {fallback_error}")
                return "内容处理完全失败"
        else:
            return "内容清理失败"