#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CDN文件上传工具
基于 fex-cloud-api.apusai.com 服务
"""

import hashlib
import time
import random
import string
import requests
import os
from typing import Dict, Any, Optional
from config.logger import setup_logging
import io

try:
    from requests_toolbelt.multipart.encoder import MultipartEncoder
    HAS_TOOLBELT = True
except ImportError:
    HAS_TOOLBELT = False

TAG = "CDNUploader"


class CDNUploader:
    """CDN文件上传器"""
    
    def __init__(self, app_id: str = "f4a73d4e", app_key: str = "ec76c7c23b25a80f8bb07536d6594c88"):
        """
        初始化CDN上传器
        
        Args:
            app_id: 应用ID
            app_key: 应用密钥
        """
        self.app_id = app_id
        self.app_key = app_key
        self.base_url = "https://fex-cloud-api.apusai.com/storage"
        self.logger = setup_logging()
    
    def generate_random_alpha_num(self, length: int) -> str:
        """生成随机字母数字字符串"""
        chars = string.ascii_lowercase + string.digits
        return ''.join(random.choice(chars) for _ in range(length))
    
    def generate_signature(self, key: str) -> str:
        """
        生成签名
        
        Args:
            key: 应用密钥
            
        Returns:
            格式化的签名字符串
        """
        now = int(time.time() * 1000)  # 毫秒时间戳
        salt = self.generate_random_alpha_num(6)
        
        # 生成MD5签名
        signature_string = f"{now}{key}{salt}"
        signature = hashlib.md5(signature_string.encode()).hexdigest()
        
        return f"{signature},{now},{salt}"
    
    def _get_file_type_and_mime(self, filename: str) -> tuple[str, str]:
        """
        根据文件名获取文件类型和MIME类型
        
        Args:
            filename: 文件名
            
        Returns:
            (file_type, mime_type): 文件类型和MIME类型元组
        """
        ext = os.path.splitext(filename)[1].lower()
        
        # 图片类型
        if ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff', '.tif', '.heif']:
            return 'image', 'image/*'
        # 视频类型
        elif ext in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv']:
            return 'video', 'video/*'
        # 音频类型
        elif ext in ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma']:
            return 'file', 'audio/*'
        # 文档类型
        elif ext in ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt']:
            return 'file', 'application/*'
        # 压缩文件
        elif ext in ['.zip', '.rar', '.7z', '.tar', '.gz']:
            return 'file', 'application/*'
        # 其他文件
        else:
            return 'file', 'application/octet-stream'
    
    def upload_file(self, file_path: str, remote_path: str = None, file_type: str = None) -> Dict[str, Any]:
        """
        上传文件到CDN
        
        Args:
            file_path: 本地文件路径
            remote_path: 远程文件路径，如果不指定则使用默认路径
            file_type: 文件类型 (image/video/file)，如果不指定则自动判断
            
        Returns:
            上传结果字典
        """
        try:
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "error": f"文件不存在: {file_path}"
                }
            
            # 获取文件名
            filename = os.path.basename(file_path)
            
            # 自动判断文件类型和MIME类型
            if not file_type:
                file_type, mime_type = self._get_file_type_and_mime(filename)
            else:
                _, mime_type = self._get_file_type_and_mime(filename)
            
            # 如果没有指定远程路径，使用默认路径
            if not remote_path:
                timestamp = int(time.time())
                remote_path = f"{file_type}s/{timestamp}/{filename}"
            
            # 生成签名
            signature = self.generate_signature(self.app_key)
            
            # 构建完整的上传URL
            upload_url = f"{self.base_url}/{file_type}"
            
            # 准备表单数据
            with open(file_path, 'rb') as f:
                # 根据文件类型选择上传方式
                if file_type == 'image':
                    # 图片类型使用原来的方式
                    files = {
                        'file': (filename, f, mime_type)
                    }
                    data = {
                        'path': remote_path
                    }
                else:
                    # 非图片类型使用multipart/form-data
                    if HAS_TOOLBELT:
                        multipart_data = MultipartEncoder(
                            fields={
                                'path': remote_path,
                                'file': (filename, f, mime_type)
                            }
                        )
                        files = None
                        data = multipart_data
                    else:
                        # 回退到普通方式
                        files = {
                            'file': (filename, f, mime_type)
                        }
                        data = {
                            'path': remote_path
                        }
                
                headers = {
                    'X-LC-Id': self.app_id,
                    'X-LC-Sign': signature
                }
                
                # 对于非图片文件，添加multipart content-type
                if file_type != 'image' and HAS_TOOLBELT and hasattr(data, 'content_type'):
                    headers['Content-Type'] = data.content_type
                
                self.logger.bind(tag=TAG).info(f"开始上传文件: {file_path} -> {remote_path} (类型: {file_type})")
                
                # 发起上传请求
                if file_type == 'image' or not HAS_TOOLBELT:
                    response = requests.post(
                        upload_url,
                        files=files,
                        data=data,
                        headers=headers,
                        timeout=30
                    )
                else:
                    response = requests.post(
                        upload_url,
                        data=data,
                        headers=headers,
                        timeout=30
                    )
                
                response.raise_for_status()
                result = response.json()
                
                self.logger.bind(tag=TAG).info(f"文件上传成功: {result}")
                
                # 提取URL，优先使用publicURL
                url = ""
                if "data" in result and isinstance(result["data"], dict):
                    data = result["data"]
                    url = data.get("publicURL") or data.get("url") or ""
                    if not url and data.get("host") and data.get("url"):
                        url = f"{data['host']}/{data['url']}"
                else:
                    url = result.get("url", "")
                
                return {
                    "success": True,
                    "data": result,
                    "url": url,
                    "path": remote_path
                }
                
        except requests.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            self.logger.bind(tag=TAG).error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
        except Exception as e:
            error_msg = f"上传文件失败: {str(e)}"
            self.logger.bind(tag=TAG).error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    def upload_bytes(self, file_bytes: bytes, filename: str, remote_path: str = None, file_type: str = None) -> Dict[str, Any]:
        """
        上传字节数据到CDN
        
        Args:
            file_bytes: 文件字节数据
            filename: 文件名
            remote_path: 远程文件路径
            file_type: 文件类型 (image/video/file)，如果不指定则自动判断
            
        Returns:
            上传结果字典
        """
        try:
            # 自动判断文件类型和MIME类型
            if not file_type:
                file_type, mime_type = self._get_file_type_and_mime(filename)
            else:
                _, mime_type = self._get_file_type_and_mime(filename)
            
            # 如果没有指定远程路径，使用默认路径
            if not remote_path:
                timestamp = int(time.time())
                remote_path = f"{file_type}s/{timestamp}/{filename}"
            
            # 生成签名
            signature = self.generate_signature(self.app_key)
            
            # 构建完整的上传URL
            upload_url = f"{self.base_url}/{file_type}"
            
            # 准备表单数据
            headers = {
                'X-LC-Id': self.app_id,
                'X-LC-Sign': signature
            }
            
            # 根据文件类型选择上传方式
            if file_type == 'image':
                # 图片类型使用原来的方式
                files = {
                    'file': (filename, file_bytes, mime_type)
                }
                data = {
                    'path': remote_path
                }
            else:
                # 非图片类型使用multipart/form-data
                if HAS_TOOLBELT:
                    multipart_data = MultipartEncoder(
                        fields={
                            'path': remote_path,
                            'file': (filename, file_bytes, mime_type)
                        }
                    )
                    files = None
                    data = multipart_data
                    headers['Content-Type'] = multipart_data.content_type
                else:
                    # 回退到普通方式
                    files = {
                        'file': (filename, file_bytes, mime_type)
                    }
                    data = {
                        'path': remote_path
                    }
            
            self.logger.bind(tag=TAG).info(f"开始上传字节数据: {filename} ({len(file_bytes)} bytes) -> {remote_path} (类型: {file_type})")
            
            # 发起上传请求
            if file_type == 'image' or not HAS_TOOLBELT:
                response = requests.post(
                    upload_url,
                    files=files,
                    data=data,
                    headers=headers,
                    timeout=30
                )
            else:
                response = requests.post(
                    upload_url,
                    data=data,
                    headers=headers,
                    timeout=30
                )
            
            response.raise_for_status()
            result = response.json()
            
            self.logger.bind(tag=TAG).info(f"字节数据上传成功: {result}")
            
            # 提取URL，优先使用publicURL
            url = ""
            if "data" in result and isinstance(result["data"], dict):
                data = result["data"]
                url = data.get("publicURL") or data.get("url") or ""
                if not url and data.get("host") and data.get("url"):
                    url = f"{data['host']}/{data['url']}"
            else:
                url = result.get("url", "")
            
            return {
                "success": True,
                "data": result,
                "url": url,
                "path": remote_path
            }
            
        except requests.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            self.logger.bind(tag=TAG).error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
        except Exception as e:
            error_msg = f"上传字节数据失败: {str(e)}"
            self.logger.bind(tag=TAG).error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }