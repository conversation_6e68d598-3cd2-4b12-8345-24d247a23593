import queue
import json
import asyncio
import time
import threading
import traceback

TAG = "connection_cleanup"

async def handle_barge_in(conn):
    """
    处理用户语音打断（Barge-in）逻辑。
    当用户在AI讲话时开始说话，此函数被调用以中断AI。
    """
    # 检查是否真的需要打断（如果AI没在说话，则无需操作）
    if conn.asr_server_receive:
        return

    conn.logger.bind(tag=TAG).info(f"VAD: {conn.client_id}. 打断AI输出.")

    # 1. 设置中断标志，这将阻止新的LLM内容生成和TTS任务处理
    conn.client_abort = True
    conn.logger.bind(tag=TAG).debug(f"🔄 设置client_abort=True，会话ID: {conn.session_id}")

    # 2. 如果使用TTSManager，中断其任务
    if conn.tts_manager and conn.client_id:
        conn.tts_manager.interrupt_client_tasks(conn.client_id)

    # 3. 清理ConnectionHandler自己的后备队列（当不使用TTSManager时）
    while not conn.tts_queue.empty():
        try:
            future, _ = conn.tts_queue.get_nowait()
            if future and not future.done():
                future.cancel()
        except (queue.Empty, asyncio.CancelledError):
            break
    
    while not conn.audio_play_queue.empty():
        try:
            conn.audio_play_queue.get_nowait()
        except queue.Empty:
            break

    # 4. 向客户端发送停止播放指令
    try:
        if conn.websocket and hasattr(conn.websocket, 'state'):
            from websockets.protocol import State
            if conn.websocket.state == State.OPEN:
                await conn.websocket.send(
                    json.dumps({"type": "tts", "state": "stop", "session_id": conn.session_id})
                )
                conn.logger.bind(tag=TAG).debug("Sent TTS stop message to client.")
    except Exception as e:
        conn.logger.bind(tag=TAG).warning(f"Failed to send TTS stop message: {e}")

    # 5. 清除服务器端的讲话状态，准备接收新指令
    clear_speak_status(conn)


def clear_speak_status(conn):
    """清除服务端讲话状态"""
    conn.logger.bind(tag=TAG).debug(f"清除服务端讲话状态")
    conn.asr_server_receive = True
    conn.tts_last_text_index = -1
    conn.tts_first_text_index = -1


def clear_queues(conn, send_poison_pill=False):
    """清空任务队列

    Args:
        conn: ConnectionHandler实例
        send_poison_pill (bool): 是否发送毒丸信号终止线程
            - False: 只清空队列，不终止线程（用于用户说话中断）
            - True: 清空队列并终止线程（用于连接关闭）
    """
    # 清空所有任务队列
    conn.logger.bind(tag=TAG).debug(
        f"开始清理: TTS队列大小={conn.tts_queue.qsize()}, 音频队列大小={conn.audio_play_queue.qsize()}, 发送毒丸信号={send_poison_pill}"
    )
    for q in [conn.tts_queue, conn.audio_play_queue]:
        if not q:
            continue
        while not q.empty():
            try:
                q.get_nowait()
            except queue.Empty:
                continue
        q.queue.clear()

        # 只有在明确要求时才发送毒丸信号
        if send_poison_pill:
            try:
                q.put(None, block=False)  # 发送毒丸信号
                conn.logger.bind(tag=TAG).debug(f"已向队列发送毒丸信号: {type(q).__name__}")
            except:
                pass
    conn.logger.bind(tag=TAG).debug(
        f"清理结束: TTS队列大小={conn.tts_queue.qsize()}, 音频队列大小={conn.audio_play_queue.qsize()}"
    )


def reset_vad_states(conn):
    """重置VAD状态"""
    conn.client_audio_buffer = bytearray()
    conn.client_have_voice = False
    conn.client_have_voice_last_time = 0
    conn.client_voice_stop = False
    conn.logger.bind(tag=TAG).debug("VAD states reset.")


def reset_chat_states(conn):
    """
    重置聊天相关状态，确保新的聊天请求不会被之前的中断影响
    
    Args:
        conn: ConnectionHandler实例
    """
    conn.logger.bind(tag=TAG).debug(f"🔄 重置聊天相关状态，准备新的对话")
    
    # 重置中断标志
    conn.client_abort = False
    
    # 重置内容拦截标志  
    if hasattr(conn, 'content_blocked'):
        conn.content_blocked = False
    if hasattr(conn, 'stream_is_blocked'):
        conn.stream_is_blocked = False
    
    # 重置TTS相关状态
    conn.asr_server_receive = False  # 设置为正在处理状态
    
    # 通知TTS Manager新的会话开始（如果存在）
    if hasattr(conn, 'tts_manager') and conn.tts_manager and hasattr(conn, 'client_id') and conn.client_id:
        conn.logger.bind(tag=TAG).debug(f"🔄 通知TTS Manager新会话开始: {conn.client_id}")
    
    conn.logger.bind(tag=TAG).debug(f"🔄 状态重置完成 - client_abort: {conn.client_abort}, asr_server_receive: {conn.asr_server_receive}")


async def cleanup_streaming_asr(conn):
    """清理流式ASR连接"""
    try:
        if hasattr(conn, 'client_id') and conn.client_id:
            from core.providers.asr.doubao_v3 import cleanup_client_streaming_asr
            await cleanup_client_streaming_asr(conn.client_id)
            conn.logger.bind(tag=TAG).info(f"🎤 已清理客户端 {conn.client_id} 的流式ASR连接")
    except Exception as e:
        conn.logger.bind(tag=TAG).error(f"🎤 清理流式ASR连接失败: {e}")


async def close_connection(conn, ws=None):
    """资源清理方法"""
    # 立即设置连接关闭标志，以便所有生成过程能够快速检测到
    conn.is_connection_closed = True
    conn.client_abort = True  # 设置客户端中断标志，立即停止LLM处理
    
    # 导入必要的模块
    from core.providers.llm.base import mark_session_closed, cleanup_session
    
    # 标记会话已关闭，立即通知LLM生成过程
    mark_session_closed(conn.session_id)
    
    # 【优先清理】流式ASR连接
    await cleanup_streaming_asr(conn)
    
    # 从TTS管理器注销客户端
    if conn.tts_manager and conn.client_id:
        conn.tts_manager.unregister_client(conn.client_id)
        conn.logger.bind(tag=TAG).info(f"客户端已从TTS管理器注销: {conn.client_id}")
    
    # 记录连接关闭的详细信息
    connection_duration = time.time() - getattr(conn, 'connection_start_time', time.time())
    
    # 分析关闭原因
    call_stack = traceback.extract_stack()
    
    # 检查关闭原因，优先使用client_disconnected标志
    close_reason = "UNKNOWN"
    close_initiator = "SERVER"
    websocket_already_closed = False
    
    # 首先检查是否是客户端断开连接
    if getattr(conn, 'client_disconnected', False):
        websocket_already_closed = True
        close_reason = "CLIENT_DISCONNECT"
        close_initiator = "CLIENT"
    elif ws is not None:
        try:
            # 检查WebSocket状态
            if hasattr(ws, 'state'):
                from websockets.protocol import State
                if ws.state in (State.CLOSING, State.CLOSED):
                    websocket_already_closed = True
                    # 检查close_code来确定关闭原因
                    if hasattr(ws, 'close_code') and ws.close_code is not None:
                        if ws.close_code == 1000:
                            close_reason = "CLIENT_NORMAL_CLOSE"
                            close_initiator = "CLIENT"
                        elif ws.close_code == 1001:
                            close_reason = "CLIENT_GOING_AWAY"
                            close_initiator = "CLIENT"
                        elif ws.close_code == 1006:
                            close_reason = "CLIENT_ABNORMAL_CLOSE"
                            close_initiator = "CLIENT"
                        else:
                            close_reason = f"CLIENT_CLOSE_CODE_{ws.close_code}"
                            close_initiator = "CLIENT"
                    else:
                        close_reason = "CLIENT_DISCONNECT"
                        close_initiator = "CLIENT"
        except Exception as e:
            conn.logger.bind(tag=TAG).debug(f"检查WebSocket状态时出错: {e}")
    
    # 检查最近几层调用栈
    stack_info = []
    for frame in call_stack[-5:]:  # 查看最近5层调用
        stack_info.append(f"{frame.filename}:{frame.lineno} in {frame.name}")
        
    stack_str = " -> ".join(stack_info)
    
    # 如果WebSocket未关闭，则根据调用栈判断服务端关闭原因
    if not websocket_already_closed:
        if any("_save_and_close" in info for info in stack_info):
            close_reason = "NORMAL_CLEANUP"
            close_initiator = "CONNECTION_HANDLER"
        elif any("handle_connection" in info for info in stack_info):
            close_reason = "CONNECTION_END"
            close_initiator = "CONNECTION_HANDLER"
        elif any("_handle_connection" in info for info in stack_info):
            close_reason = "SERVER_CLEANUP"
            close_initiator = "WEBSOCKET_SERVER"
        elif conn.client_abort:
            close_reason = "CLIENT_ABORT"
            close_initiator = "CLIENT"
    
    # 根据实际发起方显示不同的日志消息
    if close_initiator == "CLIENT":
        connection_status = "客户端断开连接"
    else:
        connection_status = "服务端关闭连接"
        
    conn.logger.bind(tag=TAG).warning(
        f"🔌 {connection_status} - "
        f"原因: {close_reason}, "
        f"发起方: {close_initiator}, "
        f"会话ID: {conn.session_id}, "
        f"客户端: {conn.client_id}, "
        f"设备ID: {conn.device_id}, "
        f"连接时长: {connection_duration:.2f}秒"
    )
    
    # 在DEBUG级别记录详细调用栈
    conn.logger.bind(tag=TAG).debug(f"🔌 关闭调用栈: {stack_str}")
    
    # 【优先处理】立即清理TTS连接，防止CPU占用问题
    try:
        conn.logger.bind(tag=TAG).info(f"🔌 【优先清理】开始清理TTS连接")
        try:
            # Use TTSManager to clean up client connections instead of calling TTS providers directly
            if hasattr(conn, 'tts_manager') and conn.tts_manager and conn.client_id:
                conn.tts_manager.unregister_client(conn.client_id)
                conn.logger.bind(tag=TAG).info(f"🔌 【优先清理】TTS连接清理完成")
            
            # 【CPU修复】等待一小段时间确保TTS工作协程完全退出
            await asyncio.sleep(0.5)  # 等待500ms让TTS协程完全停止
            conn.logger.bind(tag=TAG).info(f"🔌 【优先清理】TTS协程停止等待完成")
        except Exception as e:
            conn.logger.bind(tag=TAG).error(f"🔌 【优先清理】清理TTS连接时出错: {e}")
            # 即使TTS清理失败，也要尝试强制停止
            try:
                await asyncio.sleep(0.1)  # 短暂等待
                conn.logger.bind(tag=TAG).warning(f"🔌 【优先清理】TTS清理失败，已尝试强制等待")
            except:
                pass
    except Exception as fatal_e:
        conn.logger.bind(tag=TAG).error(f"🔌 【优先清理】TTS清理出现致命错误: {fatal_e}")
    finally:
        conn.logger.bind(tag=TAG).info(f"🔌 【优先清理】TTS清理流程完成（无论成功或失败）")
    

    # TTS connections are already cleaned up by TTSManager in the priority cleanup section above
    # No need for additional TTS cleanup here

    # 保存用户设置到Redis（使用user_profile_manager统一管理）
    if hasattr(conn, 'user_profile_manager') and conn.user_profile_manager:
        try:
            conn.user_profile_manager.save_user_settings()
            conn.logger.bind(tag=TAG).info(f"已保存用户 {conn.client_id} 的设置")
        except Exception as e:
            conn.logger.bind(tag=TAG).error(f"保存用户 {conn.client_id} 的设置失败: {e}")

    # 移除超时任务清理逻辑

    # 清理MCP资源
    if hasattr(conn, "mcp_manager") and conn.mcp_manager:
        await conn.mcp_manager.cleanup_all()

    # 触发停止事件并清理资源
    if conn.stop_event:
        conn.stop_event.set()

    # 立即关闭线程池
    conn.logger.bind(tag=TAG).info(f"🔌 断开连接：开始清理ThreadPoolExecutor")
    if conn.executor:
        try:
            conn.logger.bind(tag=TAG).info(f"🔌 ThreadPoolExecutor存在，开始清理")
            # 【CPU修复】先尝试取消所有正在等待的futures，防止CPU占用
            # 获取所有提交到executor但尚未完成的futures
            submitted_futures = []
            # 从TTS队列中获取所有等待的futures
            temp_tts_items = []
            tts_queue_size = conn.tts_queue.qsize()
            conn.logger.bind(tag=TAG).info(f"🔌 TTS队列大小: {tts_queue_size}")
            
            while not conn.tts_queue.empty():
                try:
                    item = conn.tts_queue.get_nowait()
                    temp_tts_items.append(item)
                    if hasattr(item, '__iter__') and len(item) >= 1:
                        future = item[0]
                        if hasattr(future, 'cancel'):
                            submitted_futures.append(future)
                except:
                    break
            
            conn.logger.bind(tag=TAG).info(f"🔌 从TTS队列中发现 {len(submitted_futures)} 个futures")
            
            # 将未处理的项重新放回队列，以便后续可能的调试或记录
            for item in temp_tts_items:
                conn.tts_queue.put(item)
            
            # 取消所有等待的future
            cancelled_count = 0
            for future in submitted_futures:
                if not future.done():
                    if future.cancel():
                        cancelled_count += 1
            conn.logger.bind(tag=TAG).info(f"🔌 已取消 {cancelled_count} 个等待中的TTS任务")

            # 【CPU修复】使用毒丸信号优雅地停止工作线程
            conn.logger.bind(tag=TAG).info(f"🔌 发送毒丸信号到TTS队列")
            conn.tts_queue.put(None) # 发送毒丸
            conn.logger.bind(tag=TAG).info(f"🔌 发送毒丸信号到音频播放队列")
            conn.audio_play_queue.put(None) # 发送毒丸
            conn.logger.bind(tag=TAG).info(f"🔌 发送毒丸信号到上报队列")
            conn.report_queue.put(None) # 发送毒丸

            # 等待线程结束
            if hasattr(conn, 'tts_priority_thread') and conn.tts_priority_thread.is_alive():
                conn.logger.bind(tag=TAG).info(f"🔌 等待TTS线程结束...")
                conn.tts_priority_thread.join(timeout=2.0) # 等待2秒
                if conn.tts_priority_thread.is_alive():
                    conn.logger.bind(tag=TAG).warning(f"🔌 TTS线程在2秒后仍未结束")
            
            if hasattr(conn, 'audio_play_priority_thread') and conn.audio_play_priority_thread.is_alive():
                conn.logger.bind(tag=TAG).info(f"🔌 等待音频播放线程结束...")
                conn.audio_play_priority_thread.join(timeout=2.0) # 等待2秒
                if conn.audio_play_priority_thread.is_alive():
                    conn.logger.bind(tag=TAG).warning(f"🔌 音频播放线程在2秒后仍未结束")
            
            if hasattr(conn, 'report_thread') and conn.report_thread and conn.report_thread.is_alive():
                conn.logger.bind(tag=TAG).info(f"🔌 等待上报线程结束...")
                conn.report_thread.join(timeout=2.0) # 等待2秒
                if conn.report_thread.is_alive():
                    conn.logger.bind(tag=TAG).warning(f"🔌 上报线程在2秒后仍未结束")

            # 关闭线程池
            conn.logger.bind(tag=TAG).info(f"🔌 开始关闭ThreadPoolExecutor")
            conn.executor.shutdown(wait=False) # 不再等待，因为任务可能已经被取消
            conn.logger.bind(tag=TAG).info(f"🔌 ThreadPoolExecutor已关闭")

        except Exception as e:
            conn.logger.bind(tag=TAG).error(f"🔌 清理ThreadPoolExecutor时出错: {e}")
            
            # 取消所有等待中的futures (重新获取，因为可能在try块中失败)
            submitted_futures = []
            temp_tts_items = []
            while not conn.tts_queue.empty():
                try:
                    item = conn.tts_queue.get_nowait()
                    temp_tts_items.append(item)
                    if hasattr(item, '__iter__') and len(item) >= 1:
                        future = item[0]
                        if hasattr(future, 'cancel'):
                            submitted_futures.append(future)
                except:
                    break
            
            # 将未处理的项重新放回队列
            for item in temp_tts_items:
                conn.tts_queue.put(item)
            
            # 取消所有等待中的futures
            cancelled_count = 0
            for future in submitted_futures:
                try:
                    if future.cancel():
                        cancelled_count += 1
                except:
                    pass
            
            if cancelled_count > 0:
                conn.logger.bind(tag=TAG).info(f"🔌 已取消 {cancelled_count} 个等待中的TTS任务")
            else:
                conn.logger.bind(tag=TAG).info(f"🔌 无等待中的TTS任务需要取消")
            
            # 使用兼容的方式关闭executor
            conn.logger.bind(tag=TAG).info(f"🔌 开始关闭ThreadPoolExecutor")
            try:
                # 尝试使用Python 3.9+的参数
                conn.executor.shutdown(wait=False, cancel_futures=True)
                conn.logger.bind(tag=TAG).info(f"🔌 ThreadPoolExecutor已关闭 (使用cancel_futures=True)")
            except TypeError:
                # Python 3.8及以下版本不支持cancel_futures参数
                conn.executor.shutdown(wait=False)
                conn.logger.bind(tag=TAG).info(f"🔌 ThreadPoolExecutor已关闭 (不支持cancel_futures参数)")
                
            # 【CPU修复增强】强制终止所有executor线程，防止CPU占用100%
            active_threads = threading.active_count()
            conn.logger.bind(tag=TAG).info(f"🔌 当前活跃线程数: {active_threads}")
            
            # 获取executor内部的线程并强制清理
            if hasattr(conn.executor, '_threads'):
                executor_threads = getattr(conn.executor, '_threads', set())
                conn.logger.bind(tag=TAG).info(f"🔌 Executor线程数: {len(executor_threads)}")
                
                # 创建一个新的set来避免迭代时修改原集合
                threads_to_clean = list(executor_threads)
                for thread in threads_to_clean:
                    if thread.is_alive():
                        conn.logger.bind(tag=TAG).warning(f"🔌 发现存活的executor线程: {thread.name}")
                        
                # 【关键修复】清空executor的线程集合，防止悬挂引用
                executor_threads.clear()
                conn.logger.bind(tag=TAG).info(f"🔌 已清空executor线程集合")
                        
            # 等待一小段时间让线程自然退出，然后强制清理
            time.sleep(0.2)
            
            # 【额外清理】强制设置executor为broken状态
            if hasattr(conn.executor, '_broken'):
                conn.executor._broken = True
                conn.logger.bind(tag=TAG).info(f"🔌 已设置executor为broken状态")
            
            final_active_threads = threading.active_count()
            conn.logger.bind(tag=TAG).info(f"🔌 最终活跃线程数: {final_active_threads}")
            
            # 如果仍有非daemon线程，记录警告
            remaining_non_daemon = []
            for t in threading.enumerate():
                if t.is_alive() and not t.daemon and 'ThreadPoolExecutor' in t.name:
                    remaining_non_daemon.append(t.name)
                    
            if remaining_non_daemon:
                conn.logger.bind(tag=TAG).warning(f"🔌 仍有 {len(remaining_non_daemon)} 个non-daemon executor线程存活: {remaining_non_daemon}")
                conn.logger.bind(tag=TAG).warning(f"🔌 这些线程现在应该是daemon线程，进程退出时会自动终止")
        except Exception as e:
            conn.logger.bind(tag=TAG).warning(f"🔌 关闭ThreadPoolExecutor时出错: {e}")
            conn.logger.bind(tag=TAG).error(f"🔌 ThreadPoolExecutor关闭错误堆栈: {traceback.format_exc()}")
        finally:
            conn.executor = None
            conn.logger.bind(tag=TAG).info(f"🔌 ThreadPoolExecutor清理完成")
    else:
        conn.logger.bind(tag=TAG).info(f"🔌 无ThreadPoolExecutor需要清理")

    # 添加毒丸对象到上报队列确保线程退出
    conn.logger.bind(tag=TAG).info(f"🔌 断开连接：添加毒丸到上报队列")
    conn.report_queue.put(None)

    # 清空任务队列并终止线程
    conn.logger.bind(tag=TAG).info(f"🔌 断开连接：开始清空任务队列并终止线程")
    clear_queues(conn, send_poison_pill=True)

    # 关闭WebSocket连接
    conn.logger.bind(tag=TAG).info(f"🔌 断开连接：关闭WebSocket")
    if ws:
        await ws.close()
        conn.logger.bind(tag=TAG).info(f"🔌 传入的WebSocket已关闭")
    elif conn.websocket:
        await conn.websocket.close()
        conn.logger.bind(tag=TAG).info(f"🔌 实例WebSocket已关闭")
    
    # TTS清理已在方法开始时优先处理，避免重复清理
    conn.logger.bind(tag=TAG).info(f"🔌 TTS清理已在优先阶段完成，跳过重复清理")
    
    conn.logger.bind(tag=TAG).info("🔌 连接资源已释放")
    
    # 【CPU修复调试】列出所有仍然活跃的线程
    active_threads = threading.enumerate()
    conn.logger.bind(tag=TAG).info(f"🔌 当前所有活跃线程 ({len(active_threads)}):")
    for thread in active_threads:
        conn.logger.bind(tag=TAG).info(f"  - {thread.name} (daemon={thread.daemon}, alive={thread.is_alive()})")
    
    # 清理会话状态，防止内存泄漏
    conn.logger.bind(tag=TAG).info(f"🔌 断开连接：清理会话状态")
    cleanup_session(conn.session_id)
    conn.logger.bind(tag=TAG).info(f"🔌 会话状态清理完成")
    
    # 【重要修复】不要取消所有异步任务！这会导致服务器退出
    # 只记录当前任务状态，不进行任何取消操作
    try:
        try:
            current_loop = asyncio.get_running_loop()
            if current_loop and not current_loop.is_closed():
                # 只记录任务状态，不取消任何任务
                pending_tasks = [task for task in asyncio.all_tasks(current_loop) if not task.done()]
                conn.logger.bind(tag=TAG).info(f"🔌 当前有 {len(pending_tasks)} 个运行中的异步任务")
                
                # 记录主要任务状态（用于调试），但不取消
                main_tasks = []
                connection_tasks = []
                for task in pending_tasks:
                    task_name = str(task.get_coro())
                    if 'main()' in task_name or 'start()' in task_name or 'monitor_stdin()' in task_name:
                        main_tasks.append(task_name)
                    elif 'handle_connection' in task_name or 'conn_handler' in task_name:
                        connection_tasks.append(task_name)
                
                if main_tasks:
                    conn.logger.bind(tag=TAG).debug(f"🔌 主要服务器任务仍在运行: {len(main_tasks)}个")
                if connection_tasks:
                    conn.logger.bind(tag=TAG).debug(f"🔌 连接处理任务: {len(connection_tasks)}个")
                    
                conn.logger.bind(tag=TAG).info(f"🔌 客户端连接清理完成，服务器继续运行")
            else:
                conn.logger.bind(tag=TAG).info(f"🔌 事件循环已关闭或不存在")
        except RuntimeError as e:
            if "no running event loop" in str(e):
                conn.logger.bind(tag=TAG).info(f"🔌 事件循环已停止")
            else:
                conn.logger.bind(tag=TAG).warning(f"🔌 获取事件循环时出错: {e}")
    except Exception as e:
        conn.logger.bind(tag=TAG).warning(f"🔌 检查异步任务状态时出错: {e}")
    
    # 【CPU调试】最终检查事件循环任务状态
    try:
        try:
            current_loop = asyncio.get_running_loop()
            all_tasks = asyncio.all_tasks(current_loop)
            running_tasks = [task for task in all_tasks if not task.done()]
            conn.logger.bind(tag=TAG).info(f"🔌 当前事件循环中的任务状态: 总任务数={len(all_tasks)}, 运行中任务数={len(running_tasks)}")
            
            # 记录前5个运行中任务的详细信息
            for i, task in enumerate(running_tasks[:5]):
                try:
                    task_name = getattr(task, '_name', 'unknown')
                    task_coro = getattr(task, '_coro', None)
                    coro_name = getattr(task_coro, '__name__', 'unknown') if task_coro else 'unknown'
                    conn.logger.bind(tag=TAG).info(f"🔌 运行中任务 #{i+1}: name={task_name}, coro={coro_name}, done={task.done()}, cancelled={task.cancelled()}")
                except Exception as e:
                    conn.logger.bind(tag=TAG).warning(f"🔌 获取任务 #{i+1} 信息失败: {e}")
        except RuntimeError as e:
            if "no running event loop" in str(e):
                conn.logger.bind(tag=TAG).info(f"🔌 事件循环已停止，无法检查异步任务状态")
            else:
                conn.logger.bind(tag=TAG).warning(f"🔌 检查事件循环时出错: {e}")
    except Exception as e:
        conn.logger.bind(tag=TAG).error(f"🔌 检查异步任务状态失败: {e}")
        
    conn.logger.bind(tag=TAG).info(f"🔌🔌🔌 客户端 {conn.client_id} 完全断开连接处理完成 🔌🔌🔌")