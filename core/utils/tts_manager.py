
import asyncio
import os
import queue
import threading
import time
from concurrent.futures import Future, TimeoutError, ThreadPoolExecutor
from collections import deque
from typing import Dict, Optional, Tuple, Any

from config.logger import setup_logging
from core.handle.reportHandle import enqueue_tts_report
from core.handle.sendAudioHandle import sendAudioMessage

TAG = __name__


class TTSManager:
    def __init__(self, config, tts_providers, loop):
        self.logger = setup_logging()
        self.config = config
        self.tts_providers = tts_providers
        self.loop = loop
        self.clients = {}
        self.lock = threading.Lock()
        
        # Task counter for priority queue tiebreaker (thread-safe)
        self.task_counter = 0
        self.task_counter_lock = threading.Lock()
        
        # 预加载配置
        self.preload_enabled = config.get('tts', {}).get('preload_enabled', True)
        self.max_preload_count = config.get('tts', {}).get('max_preload_count', 3)
        self.preload_timeout = config.get('tts', {}).get('preload_timeout', 10.0)
        
        # 并行TTS生成的线程池
        self.tts_executor = ThreadPoolExecutor(
            max_workers=config.get('tts', {}).get('max_concurrent_tts', 4),
            thread_name_prefix="TTSGen"
        )
        
        self.logger.bind(tag=TAG).info(
            f"TTSManager初始化完成 - 预加载: {self.preload_enabled}, "
            f"最大预加载数: {self.max_preload_count}, "
            f"并发TTS线程: {self.tts_executor._max_workers}"
        )
    
    def _get_next_task_id(self):
        """线程安全的任务ID生成器"""
        with self.task_counter_lock:
            self.task_counter += 1
            return self.task_counter

    def register_client(self, client_id, conn_handler):
        with self.lock:
            if client_id not in self.clients:
                self.logger.bind(tag=TAG).info(f"Registering new client: {client_id}")
                client_data = {
                    "conn_handler": conn_handler,
                    "tts_queue": queue.PriorityQueue(),  # 优先级队列
                    "audio_play_queue": queue.Queue(),
                    "preload_cache": {},  # 预加载缓存 {text_index: (audio_datas, text)}
                    "preload_futures": {},  # 预加载Future {text_index: future}
                    "current_playing_index": -1,  # 当前播放的句子索引
                    "stop_event": threading.Event(),
                    "tts_thread": None,
                    "audio_thread": None,
                    "preload_thread": None,
                    "last_submission_time": time.time(),
                    "pending_audio_buffer": {},  # 等待排序的音频缓冲区 {text_index: (audio_datas, text)}
                    "next_expected_index": 1,  # 下一个期待播放的句子索引（从1开始）
                }
                self.clients[client_id] = client_data
                self._start_worker_threads(client_id)

    def unregister_client(self, client_id):
        with self.lock:
            if client_id in self.clients:
                self.logger.bind(tag=TAG).info(f"Unregistering client: {client_id}")
                client_data = self.clients[client_id]
                client_data["stop_event"].set()

                # 取消所有预加载任务
                for future in client_data["preload_futures"].values():
                    if not future.done():
                        future.cancel()
                
                # 取消所有正在队列中等待的TTS任务
                cancelled_tasks = []
                while not client_data["tts_queue"].empty():
                    try:
                        priority, tiebreaker, task_data = client_data["tts_queue"].get_nowait()
                        if task_data:
                            future = task_data[0]
                            if future and not future.done():
                                future.cancel()
                                cancelled_tasks.append(task_data[2])  # text_index
                    except Exception:
                        break
                
                if cancelled_tasks:
                    self.logger.bind(tag=TAG).info(f"[{client_id}] 取消了 {len(cancelled_tasks)} 个待处理的TTS任务")
                
                # Send poison pills to unblock queues
                client_data["tts_queue"].put((0, self._get_next_task_id(), None))  # 优先级队列需要元组
                client_data["audio_play_queue"].put(None)

                # Wait for threads to finish
                threads_to_join = [
                    ("tts_thread", client_data["tts_thread"]),
                    ("audio_thread", client_data["audio_thread"]),
                    ("preload_thread", client_data["preload_thread"])
                ]
                
                for thread_name, thread in threads_to_join:
                    if thread and thread.is_alive():
                        thread.join(timeout=2)
                        if thread.is_alive():
                            self.logger.bind(tag=TAG).warning(
                                f"{thread_name} for client {client_id} did not stop gracefully"
                            )

                del self.clients[client_id]
                self.logger.bind(tag=TAG).info(f"Client {client_id} unregistered.")

    def submit_task(self, client_id, text, text_index, voice_engine=None, voice=None, priority=1):
        """提交TTS任务
        
        Args:
            client_id: 客户端ID
            text: 要转换的文本
            text_index: 文本索引
            voice_engine: TTS引擎（可选，会从用户配置获取）
            voice: 音色（可选，会从用户配置获取）
            priority: 任务优先级 (0=最高优先级, 1=正常, 2=预加载)
        """
        with self.lock:
            if client_id in self.clients:
                client_data = self.clients[client_id]
                client_data["last_submission_time"] = time.time()
                
                # 如果没有提供voice配置，尝试从conn_handler的UserProfileManager获取
                conn_handler = client_data["conn_handler"]
                if (not voice or not voice_engine) and hasattr(conn_handler, 'user_profile_manager') and conn_handler.user_profile_manager:
                    voice_config = conn_handler.user_profile_manager.get_current_voice_config()
                    if not voice:
                        voice = voice_config.get('voice')
                    if not voice_engine:
                        voice_engine = voice_config.get('voice_engine', 'DoubaoTTS')
                    
                    if voice and voice_engine:
                        self.logger.bind(tag=TAG).debug(f"[{client_id}] 从UserProfileManager获取声音配置: {voice} ({voice_engine})")
                
                # 使用默认值兜底
                if not voice_engine:
                    voice_engine = 'DoubaoTTS'
                
                # 检查预加载缓存
                if text_index in client_data["preload_cache"]:
                    self.logger.bind(tag=TAG).info(
                        f"[{client_id}] 使用预加载缓存 sentence_{text_index}: {text[:30]}..."
                    )
                    audio_datas, cached_text = client_data["preload_cache"].pop(text_index)
                    # 使用有序入队方法
                    self._enqueue_audio_in_order(client_id, audio_datas, cached_text, text_index)
                    
                    # 立即返回已完成的Future
                    future = Future()
                    future.set_result((None, cached_text, text_index))
                    
                    # 触发下一句预加载
                    if self.preload_enabled:
                        self._trigger_preload(client_id, text_index + 1, voice_engine, voice)
                    
                    return future
                
                # 正常TTS任务
                future = Future()
                task = (priority, self._get_next_task_id(), (future, text, text_index, voice_engine, voice, time.time()))
                client_data["tts_queue"].put(task)
                
                # 触发预加载
                if self.preload_enabled and priority <= 1:  # 只为正常任务触发预加载
                    self._trigger_preload(client_id, text_index + 1, voice_engine, voice)
                
                return future
            else:
                self.logger.bind(tag=TAG).warning(
                    f"Client {client_id} not registered. Cannot submit TTS task."
                )
                return None

    """
    关于中断逻辑的说明 (interrupt_client_tasks vs unregister_client):

    `tts_manager.py` 中存在两种处理中断的逻辑，分别应对不同场景：

    1. unregister_client (断线中断):
       - 触发时机：当用户关闭客户端，WebSocket 连接断开时。
       - 目标：彻底终结和清理该用户相关的所有后台任务，释放资源。
       - 实现方式：通过设置 `stop_event` 并向队列放入“毒丸”来完全停止后台线程。这是一种“硬停止”。

    2. interrupt_client_tasks (语音打断/Barge-in):
       - 触发时机：当用户在AI讲话时，开始说下一句话。
       - 目标：立刻丢弃所有AI“正准备说”或“将要说”的话，但保持后台线程存活，以处理后续的TTS任务。
       - 实现方式：主动清空任务队列，并取消队列中所有待处理的任务，但不会停止后台线程。这是一种“软重置”。

    为什么不能合并？
    - 核心目标不同：`unregister_client` 是“人走茶凉”，要关掉服务员（线程）；`interrupt_client_tasks` 是“客人改主意了”，要让服务员（线程）别上之前的菜，马上准备新菜。
    - 如果在语音打断时调用 `unregister_client`，后台线程会被关闭，用户将再也听不到AI的声音。
    - 反之，如果在连接断开时只调用 `interrupt_client_tasks`，后台线程会一直空转，造成资源泄漏。

    因此，`interrupt_client_tasks` 是专门为“语音打断”功能服务的，它与原有的“断线清理”逻辑 `unregister_client` 互补，两者不可或缺。
    """
    def interrupt_client_tasks(self, client_id: str):
        """中断并清理指定客户端的所有TTS和音频任务"""
        with self.lock:
            if client_id in self.clients:
                self.logger.bind(tag=TAG).info(f"Interrupting all tasks for client: {client_id}")
                client_data = self.clients[client_id]
                
                # 清理TTS任务队列
                while not client_data["tts_queue"].empty():
                    try:
                        # PriorityQueue items are tuples, get the future from it
                        priority, tiebreaker, task_data = client_data["tts_queue"].get_nowait()
                        if task_data:
                            future = task_data[0]
                            if future and not future.done():
                                future.cancel()
                    except queue.Empty:
                        break
                
                # 清理音频播放队列
                while not client_data["audio_play_queue"].empty():
                    try:
                        client_data["audio_play_queue"].get_nowait()
                    except queue.Empty:
                        break
                
                # 取消所有正在进行的预加载任务
                for future in client_data["preload_futures"].values():
                    if not future.done():
                        future.cancel()
                client_data["preload_futures"].clear()
                client_data["preload_cache"].clear()
                
                # 重置音频排序状态
                client_data["pending_audio_buffer"].clear()
                client_data["next_expected_index"] = 1
                self.logger.bind(tag=TAG).info(f"[{client_id}] 已重置音频排序状态")

    def _enqueue_audio_in_order(self, client_id, audio_datas, text, text_index):
        """确保音频按text_index顺序入队播放"""
        with self.lock:
            if client_id not in self.clients:
                self.logger.bind(tag=TAG).warning(f"[{client_id}] 客户端不存在，无法入队音频 sentence_{text_index}")
                return
            
            client_data = self.clients[client_id]
            
            # 将音频数据存入待排序缓冲区
            client_data["pending_audio_buffer"][text_index] = (audio_datas, text, text_index)
            self.logger.bind(tag=TAG).info(
                f"[{client_id}] 音频存入缓冲区 sentence_{text_index}, 当前期待: {client_data['next_expected_index']}"
            )
            
            # 尝试将连续的音频数据按顺序转移到播放队列
            while client_data["next_expected_index"] in client_data["pending_audio_buffer"]:
                expected_index = client_data["next_expected_index"]
                buffered_audio = client_data["pending_audio_buffer"].pop(expected_index)
                
                # 入队播放
                client_data["audio_play_queue"].put(buffered_audio)
                client_data["next_expected_index"] += 1
                
                self.logger.bind(tag=TAG).info(
                    f"[{client_id}] 音频按序入队 sentence_{expected_index}, 下一个期待: {client_data['next_expected_index']}"
                )
                
                # 记录缓冲区状态
                if client_data["pending_audio_buffer"]:
                    buffered_indices = sorted(client_data["pending_audio_buffer"].keys())
                    self.logger.bind(tag=TAG).debug(
                        f"[{client_id}] 缓冲区剩余句子: {buffered_indices}"
                    )

    def _start_worker_threads(self, client_id):
        client_data = self.clients[client_id]
        client_data["tts_thread"] = threading.Thread(
            target=self._tts_worker, args=(client_id,), daemon=True
        )
        client_data["audio_thread"] = threading.Thread(
            target=self._audio_worker, args=(client_id,), daemon=True
        )
        client_data["preload_thread"] = threading.Thread(
            target=self._preload_worker, args=(client_id,), daemon=True
        )
        client_data["tts_thread"].start()
        client_data["audio_thread"].start()
        client_data["preload_thread"].start()

    def _tts_worker(self, client_id):
        while True:
            try:
                client_data = self.clients.get(client_id)
                if not client_data or client_data["stop_event"].is_set():
                    break

                priority_task = client_data["tts_queue"].get(timeout=1)
                if priority_task is None or len(priority_task) < 3 or priority_task[2] is None:  # Poison pill
                    break

                priority, tiebreaker, task_data = priority_task
                future, text, text_index, voice_engine, voice, submit_time = task_data
                
                # 记录任务等待时间
                wait_time = time.time() - submit_time
                if wait_time > 1.0:
                    self.logger.bind(tag=TAG).warning(
                        f"[{client_id}] TTS任务等待时间过长: {wait_time:.2f}s, sentence_{text_index}"
                    )
                conn_handler = client_data["conn_handler"]

                if conn_handler.client_abort:
                    self.logger.bind(tag=TAG).debug(
                        f"Client {client_id} aborted, discarding TTS task sentence_{text_index}."
                    )
                    future.set_exception(Exception("Client aborted"))
                    continue

                try:
                    # 方案核心改动：将耗时任务提交到共享线程池，而不是自己执行
                    pool_future = self.tts_executor.submit(
                        self._process_tts_in_pool,
                        client_id, conn_handler, task_data
                    )
                    
                    # 可以在这里添加回调，将线程池的future结果传递给原始的future
                    # 这样可以让调用者（connection.py）的逻辑保持不变
                    def _on_done_callback(f):
                        try:
                            # 首先检查客户端是否还存在
                            with self.lock:
                                client_exists = client_id in self.clients
                            
                            if f.cancelled():
                                if not future.done():
                                    future.cancel()
                                    self.logger.bind(tag=TAG).debug(f"[{client_id}] 线程池任务取消 sentence_{text_index}")
                            elif f.exception():
                                exception = f.exception()
                                self.logger.bind(tag=TAG).error(f"[{client_id}] 线程池任务异常 sentence_{text_index}: {exception}")
                                if not future.done():
                                    future.set_exception(exception)
                            else:
                                # 线程池任务完成后，将结果设置到原始future上
                                audio_datas, text_result, text_index_result = f.result()
                                self.logger.bind(tag=TAG).debug(f"[{client_id}] 线程池任务完成，设置结果到future sentence_{text_index_result}")
                                
                                # 首先设置结果到future，确保不会timeout
                                if not future.done():
                                    future.set_result((None, text_result, text_index_result))
                                    self.logger.bind(tag=TAG).debug(f"[{client_id}] Future结果已设置 sentence_{text_index_result}")
                                else:
                                    # Future已完成，检查原因
                                    if future.cancelled():
                                        self.logger.bind(tag=TAG).debug(f"[{client_id}] Future已被取消（用户打断），跳过结果设置 sentence_{text_index_result}")
                                    else:
                                        self.logger.bind(tag=TAG).debug(f"[{client_id}] Future已完成（可能超时），跳过结果设置 sentence_{text_index_result}")
                                
                                # 只有当客户端存在、优先级<=1且Future未被取消的任务才加入播放队列
                                if client_exists and priority <= 1 and not future.cancelled():
                                    try:
                                        enqueue_tts_report(conn_handler, text_result, audio_datas)
                                        # 使用新的有序入队方法
                                        self._enqueue_audio_in_order(client_id, audio_datas, text_result, text_index_result)
                                        self.logger.bind(tag=TAG).info(f"[{client_id}] 音频已按序处理 sentence_{text_index_result}")
                                    except Exception as queue_error:
                                        self.logger.bind(tag=TAG).error(f"[{client_id}] 音频按序入队失败 sentence_{text_index_result}: {queue_error}")
                                elif future.cancelled():
                                    self.logger.bind(tag=TAG).debug(f"[{client_id}] Future已被取消，跳过音频处理 sentence_{text_index_result}")
                                elif not client_exists:
                                    self.logger.bind(tag=TAG).debug(f"[{client_id}] 客户端已断开，跳过音频处理 sentence_{text_index_result}")
                                
                        except Exception as e:
                            self.logger.bind(tag=TAG).error(f"[{client_id}] 回调函数异常 sentence_{text_index}: {e}")
                            # 设置异常时检查状态
                            if not future.done():
                                future.set_exception(e)

                    pool_future.add_done_callback(_on_done_callback)

                except Exception as e:
                    self.logger.bind(tag=TAG).error(
                        f"[{client_id}] TTS任务提交到线程池失败 sentence_{text_index}: {e}"
                    )
                    future.set_exception(e)

            except queue.Empty:
                continue
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"TTS worker error: {e}")

        self.logger.bind(tag=TAG).info(f"TTS worker for client {client_id} stopped.")
    
    def _process_tts_in_pool(self, client_id, conn_handler, task_data):
        """在共享线程池中实际执行TTS生成，这是一个独立的函数"""
        future, text, text_index, voice_engine, voice, submit_time = task_data
        
        start_time = time.time()
        self.logger.bind(tag=TAG).debug(
            f"[{client_id}] 线程池开始处理TTS任务 sentence_{text_index}: {text[:50]}..."
        )
        
        audio_datas = self._generate_tts_audio(
            text, client_id, conn_handler, voice_engine, voice
        )
        
        generation_time = time.time() - start_time
        self.logger.bind(tag=TAG).info(
            f"[{client_id}] TTS生成完成 sentence_{text_index}, 耗时: {generation_time:.2f}s"
        )
        
        return audio_datas, text, text_index
    
    def __del__(self):
        """清理资源"""
        try:
            self.tts_executor.shutdown(wait=False)
        except:
            pass

    def _audio_worker(self, client_id):
        while True:
            try:
                client_data = self.clients.get(client_id)
                if not client_data or client_data["stop_event"].is_set():
                    break

                task = client_data["audio_play_queue"].get(timeout=1)
                if task is None:  # Poison pill
                    break

                audio_datas, text, text_index = task
                conn_handler = client_data["conn_handler"]
                
                self.logger.bind(tag=TAG).info(
                    f"[{client_id}] 音频播放线程开始处理 sentence_{text_index}: {text[:30]}..."
                )

                # 检查音频数据是否有效
                if not audio_datas or len(audio_datas) == 0:
                    self.logger.bind(tag=TAG).error(
                        f"[{client_id}] 音频数据为空，跳过播放 sentence_{text_index}"
                    )
                    continue

                if conn_handler.client_abort:
                    self.logger.bind(tag=TAG).debug(
                        f"Client {client_id} aborted, discarding audio play task sentence_{text_index}."
                    )
                    continue
                
                # 更新当前播放索引
                client_data["current_playing_index"] = text_index
                
                start_time = time.time()
                future = asyncio.run_coroutine_threadsafe(
                    sendAudioMessage(conn_handler, audio_datas, text, text_index),
                    self.loop,
                )
                # Calculate a reasonable timeout based on audio length
                # 60ms per frame + 5s buffer
                timeout = (len(audio_datas) * 0.06) + 5.0
                future.result(timeout=timeout)
                
                play_time = time.time() - start_time
                self.logger.bind(tag=TAG).debug(
                    f"[{client_id}] 音频播放完成 sentence_{text_index}, 播放时长: {play_time:.2f}s"
                )

            except queue.Empty:
                continue
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"Audio worker error: {e}")

        self.logger.bind(tag=TAG).info(
            f"Audio worker for client {client_id} stopped."
        )
    
    def _preload_worker(self, client_id):
        """预加载工作线程"""
        self.logger.bind(tag=TAG).info(f"Preload worker for client {client_id} started.")
        
        while True:
            try:
                client_data = self.clients.get(client_id)
                if not client_data or client_data["stop_event"].is_set():
                    break
                
                # 检查是否需要清理过期的预加载任务
                self._cleanup_expired_preloads(client_id)
                
                time.sleep(0.5)  # 定期检查
                
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"Preload worker error: {e}")
                time.sleep(1)
        
        self.logger.bind(tag=TAG).info(f"Preload worker for client {client_id} stopped.")
    
    def _trigger_preload(self, client_id: str, target_index: int, voice_engine: str, voice: str):
        """触发预加载"""
        try:
            client_data = self.clients.get(client_id)
            if not client_data or client_data["stop_event"].is_set():
                return
            
            # 检查是否已经在预加载或已缓存
            if (target_index in client_data["preload_futures"] or 
                target_index in client_data["preload_cache"]):
                return
            
            # 检查预加载数量限制
            active_preloads = len(client_data["preload_futures"])
            if active_preloads >= self.max_preload_count:
                self.logger.bind(tag=TAG).debug(
                    f"[{client_id}] 预加载数量已达上限 {active_preloads}/{self.max_preload_count}"
                )
                return
            
            # 这里需要预测下一句的文本，暂时跳过实际实现
            # 在实际应用中，需要从LLM流或缓存中获取下一句文本
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Trigger preload error: {e}")
    
    def _cleanup_expired_preloads(self, client_id: str):
        """清理过期的预加载任务"""
        try:
            client_data = self.clients.get(client_id)
            if not client_data:
                return
            
            current_time = time.time()
            expired_indices = []
            
            # 检查预加载Future是否超时
            for text_index, future in client_data["preload_futures"].items():
                if future.done():
                    if future.exception() is None:
                        # 预加载完成，转移到缓存
                        result = future.result()
                        if result:
                            client_data["preload_cache"][text_index] = result
                            self.logger.bind(tag=TAG).debug(
                                f"[{client_id}] 预加载完成 sentence_{text_index}"
                            )
                    expired_indices.append(text_index)
                elif current_time - client_data["last_submission_time"] > self.preload_timeout:
                    # 超时取消
                    future.cancel()
                    expired_indices.append(text_index)
                    self.logger.bind(tag=TAG).debug(
                        f"[{client_id}] 预加载超时取消 sentence_{text_index}"
                    )
            
            # 清理过期项
            for text_index in expired_indices:
                client_data["preload_futures"].pop(text_index, None)
            
            # 清理过旧的缓存（超过当前播放位置太多的缓存）
            current_playing = client_data["current_playing_index"]
            old_cache_indices = [
                idx for idx in client_data["preload_cache"].keys()
                if idx < current_playing - 1  # 保留当前播放位置前1句
            ]
            for idx in old_cache_indices:
                client_data["preload_cache"].pop(idx, None)
                self.logger.bind(tag=TAG).debug(
                    f"[{client_id}] 清理过期缓存 sentence_{idx}"
                )
                
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Cleanup expired preloads error: {e}")
    
    def _generate_tts_audio(self, text: str, client_id: str, conn_handler, voice_engine: str, voice: str) -> Any:
        """生成TTS音频数据"""
        tts_provider = self.tts_providers.get(voice_engine)
        if not tts_provider:
            raise ValueError(f"TTS provider '{voice_engine}' not found.")
        
        # 优先从UserProfileManager获取声音配置
        if hasattr(conn_handler, 'user_profile_manager') and conn_handler.user_profile_manager:
            voice_config = conn_handler.user_profile_manager.get_current_voice_config()
            if voice_config.get('voice'):
                voice = voice_config['voice']
                voice_engine = voice_config['voice_engine']
                self.logger.bind(tag=TAG).debug(f"从UserProfileManager获取声音配置: {voice_config}")
        
        # Set the voice in the connection's TTS config if provided
        # Voice configuration is now managed by UserProfileManager
        # No need to update connection's _user_tts_config
        
        tts_file = tts_provider.to_tts(text, client_id, conn_handler)
        if not tts_file:
            # 检查是否是因为文本清理后为空导致的
            from core.utils.util import get_string_no_punctuation_or_emoji
            cleaned_text = get_string_no_punctuation_or_emoji(text)
            if not cleaned_text.strip():
                self.logger.bind(tag=TAG).info(f"TTS文件未生成 - 文本清理后为空，这是正常情况: '{text}'")
                # 这种情况下不抛出异常，而是返回特殊标识
                raise RuntimeError(f"TTS skipped - text is empty after cleaning: '{text}'")
            else:
                self.logger.bind(tag=TAG).error(f"TTS方法返回None - 文本: '{text}'")
                raise RuntimeError(f"TTS method returned None for text: '{text}'")
        
        if not os.path.exists(tts_file):
            self.logger.bind(tag=TAG).error(f"TTS文件不存在 - 文件路径: {tts_file}, 文本: '{text}'")
            raise RuntimeError(f"TTS file not found: '{tts_file}' for text: '{text}'")
        
        # 检查文件大小
        file_size = os.path.getsize(tts_file)
        if file_size == 0:
            self.logger.bind(tag=TAG).error(f"TTS文件为空 - 文件: {tts_file}, 文本: '{text}'")
            raise RuntimeError(f"TTS file is empty: '{tts_file}' for text: '{text}'")
        
        try:
            audio_format = conn_handler.audio_format
            if audio_format == "pcm":
                audio_datas, _ = tts_provider.audio_to_pcm_data(tts_file)
            else:
                audio_datas, _ = tts_provider.audio_to_opus_data(tts_file)
            
            return audio_datas
        finally:
            if tts_provider.delete_audio_file:
                os.remove(tts_file)
    
    def submit_preload_task(self, client_id: str, text: str, text_index: int, voice_engine: str, voice: str):
        """提交预加载任务"""
        try:
            client_data = self.clients.get(client_id)
            if not client_data or client_data["stop_event"].is_set():
                return
            
            # 检查是否已存在
            if (text_index in client_data["preload_futures"] or
                text_index in client_data["preload_cache"]):
                return
            
            self.logger.bind(tag=TAG).info(
                f"[{client_id}] 提交预加载任务 sentence_{text_index}: {text[:30]}..."
            )
            
            # 提交到线程池
            future = self.tts_executor.submit(
                self._preload_generate_audio,
                text, client_id, client_data["conn_handler"], voice_engine, voice
            )
            
            client_data["preload_futures"][text_index] = future
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Submit preload task error: {e}")
    
    def _preload_generate_audio(self, text: str, client_id: str, conn_handler, voice_engine: str, voice: str) -> Tuple[Any, str]:
        """预加载生成音频（在线程池中执行）"""
        try:
            audio_datas = self._generate_tts_audio(text, client_id, conn_handler, voice_engine, voice)
            return (audio_datas, text)
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Preload generate audio error: {e}")
            raise
    
    def reset_client_text_index(self, client_id: str):
        """重置客户端的文本索引计数器，用于新对话开始时"""
        with self.lock:
            if client_id in self.clients:
                client_data = self.clients[client_id]
                client_data["next_expected_index"] = 1
                client_data["pending_audio_buffer"].clear()
                self.logger.bind(tag=TAG).info(f"[{client_id}] 重置文本索引计数器")

    def get_client_stats(self, client_id: str) -> Optional[Dict]:
        """获取客户端统计信息"""
        client_data = self.clients.get(client_id)
        if not client_data:
            return None
        
        return {
            "preload_cache_count": len(client_data["preload_cache"]),
            "preload_futures_count": len(client_data["preload_futures"]),
            "current_playing_index": client_data["current_playing_index"],
            "tts_queue_size": client_data["tts_queue"].qsize(),
            "audio_queue_size": client_data["audio_play_queue"].qsize(),
            "pending_audio_buffer_count": len(client_data["pending_audio_buffer"]),
            "next_expected_index": client_data["next_expected_index"],
        }
