#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的XOR加解密工具
"""

import os
import secrets
from itertools import cycle

def generate_random_key(length: int = 32) -> str:
    """
    生成指定长度的随机十六进制密钥.
    
    Args:
        length: 密钥的字节长度.
        
    Returns:
        十六进制字符串表示的密钥.
    """
    return secrets.token_hex(length)

def xor_cipher(data: bytes, key: str) -> bytes:
    """
    对数据进行XOR操作.
    
    Args:
        data: 要处理的字节数据.
        key: 十六进制字符串密钥.
        
    Returns:
        处理后的字节数据.
    """
    # 将十六进制密钥转换为字节
    key_bytes = bytes.fromhex(key)
    if not key_bytes:
        raise ValueError("Key cannot be empty.")
        
    # 使用cycle来重复密钥以匹配数据长度
    encrypted_bytes = bytes([b ^ k for b, k in zip(data, cycle(key_bytes))])
    return encrypted_bytes

# 为了代码清晰，保留两个别名
xor_encrypt = xor_cipher
xor_decrypt = xor_cipher

if __name__ == '__main__':
    # --- 测试代码 ---
    print("--- Running Crypto Util Self-test ---")

    # 1. 生成密钥
    test_key = generate_random_key(16)
    print(f"Generated Key (16 bytes hex): {test_key}")
    assert len(test_key) == 32  # 16 bytes = 32 hex chars

    # 2. 准备数据
    original_data_str = "这是一段用于测试XOR加解密的秘密信息. This is a secret message for testing XOR encryption."
    original_data_bytes = original_data_str.encode('utf-8')
    print(f"Original Data (str): {original_data_str}")
    print(f"Original Data (bytes, first 50): {original_data_bytes[:50]}...")

    # 3. 加密
    encrypted_data = xor_encrypt(original_data_bytes, test_key)
    print(f"Encrypted Data (bytes, first 50): {encrypted_data[:50]}...")
    assert original_data_bytes != encrypted_data

    # 4. 解密
    decrypted_data = xor_decrypt(encrypted_data, test_key)
    print(f"Decrypted Data (bytes, first 50): {decrypted_data[:50]}...")
    assert original_data_bytes == decrypted_data

    # 5. 验证解密后的内容
    decrypted_data_str = decrypted_data.decode('utf-8')
    print(f"Decrypted Data (str): {decrypted_data_str}")
    assert original_data_str == decrypted_data_str
    
    print("\n--- Self-test Passed Successfully! ---")
