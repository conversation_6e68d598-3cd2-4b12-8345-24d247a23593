import os
import re
import sys
from config.logger import setup_logging
import importlib

logger = setup_logging()


def create_instance(class_name, *args, **kwargs):
    # 创建TTS实例
    if os.path.exists(os.path.join('core', 'providers', 'tts', f'{class_name}.py')):
        lib_name = f'core.providers.tts.{class_name}'
        if lib_name not in sys.modules:
            sys.modules[lib_name] = importlib.import_module(f'{lib_name}')
        return sys.modules[lib_name].TTSProvider(*args, **kwargs)

    raise ValueError(f"不支持的TTS类型: {class_name}，请检查该配置的type是否设置正确")


class TextCleaner:
    """
    文本清理工具，使用白名单方式只保留可读的字符
    """
    
    # 序号和编号映射
    CIRCLED_NUMBERS = {
        '①': '一', '②': '二', '③': '三', '④': '四', '⑤': '五',
        '⑥': '六', '⑦': '七', '⑧': '八', '⑨': '九', '⑩': '十',
        '⑪': '十一', '⑫': '十二', '⑬': '十三', '⑭': '十四', '⑮': '十五',
        '⑯': '十六', '⑰': '十七', '⑱': '十八', '⑲': '十九', '⑳': '二十'
    }
    
    PARENTHESIZED_NUMBERS = {
        '⒈': '一', '⒉': '二', '⒊': '三', '⒋': '四', '⒌': '五',
        '⒍': '六', '⒎': '七', '⒏': '八', '⒐': '九', '⒑': '十',
        '⒒': '十一', '⒓': '十二', '⒔': '十三', '⒕': '十四', '⒖': '十五',
        '⒗': '十六', '⒘': '十七', '⒙': '十八', '⒚': '十九', '⒛': '二十'
    }
    
    # 罗马数字映射
    ROMAN_NUMBERS = {
        'Ⅰ': '一', 'Ⅱ': '二', 'Ⅲ': '三', 'Ⅳ': '四', 'Ⅴ': '五', 'Ⅵ': '六',
        'Ⅶ': '七', 'Ⅷ': '八', 'Ⅸ': '九', 'Ⅹ': '十', 'Ⅺ': '十一', 'Ⅻ': '十二',
        'ⅰ': '一', 'ⅱ': '二', 'ⅲ': '三', 'ⅳ': '四', 'ⅴ': '五', 'ⅵ': '六',
        'ⅶ': '七', 'ⅷ': '八', 'ⅸ': '九', 'ⅹ': '十', 'ⅺ': '十一', 'ⅻ': '十二'
    }
    
    # 分数映射
    FRACTIONS = {
        '½': '二分之一', '⅓': '三分之一', '⅔': '三分之二', '¼': '四分之一', '¾': '四分之三',
        '⅕': '五分之一', '⅖': '五分之二', '⅗': '五分之三', '⅘': '五分之四',
        '⅙': '六分之一', '⅚': '六分之五',
        '⅛': '八分之一', '⅜': '八分之三', '⅝': '八分之五', '⅞': '八分之七'
    }
    
    @staticmethod
    def replace_special_symbols(text: str) -> str:
        """
        替换序号、罗马数字、分数为可读文本
        """
        if not text:
            return text
        
        original_text = text
        
        # 替换序号和编号
        for symbol, replacement in TextCleaner.CIRCLED_NUMBERS.items():
            text = text.replace(symbol, replacement)
        
        for symbol, replacement in TextCleaner.PARENTHESIZED_NUMBERS.items():
            text = text.replace(symbol, replacement)
        
        # 替换罗马数字
        for symbol, replacement in TextCleaner.ROMAN_NUMBERS.items():
            text = text.replace(symbol, replacement)
        
        # 替换分数
        for symbol, replacement in TextCleaner.FRACTIONS.items():
            text = text.replace(symbol, replacement)
        
        # 如果有替换发生，记录日志
        if text != original_text:
            logger.info(f"TTS符号替换 - 原文: {original_text.strip()}")
            logger.info(f"TTS符号替换 - 处理后: {text.strip()}")
        
        return text
    
    @staticmethod
    def _remove_trailing_emojis(text: str) -> str:
        """
        去除末尾的表情符号，避免拆句时产生无意义的片段
        
        Args:
            text: 原始文本
            
        Returns:
            str: 去除末尾表情符号后的文本
        """
        if not text:
            return text
        
        # 定义更完整的表情符号正则表达式（包括变体选择器）
        # 添加了变体选择器(\uFE0F)和组合字符支持，以及更多Unicode范围
        emoji_pattern = r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F900-\U0001F9FF\U0001FA70-\U0001FAFF\U00002600-\U000026FF\U00002700-\U000027BF\U00002B00-\U00002BFF][\uFE0F\uFE00-\uFE0F]?'
        
        # 去除末尾的表情符号和空格
        processed_text = re.sub(rf'\s*(?:{emoji_pattern})+\s*$', '', text)
        
        # 如果整个文本都是表情符号，返回空字符串
        if re.fullmatch(rf'\s*(?:{emoji_pattern}|\s)+\s*$', text):
            return ""
        
        return processed_text
    
    @staticmethod
    def clean_for_tts(text: str) -> str:
        """
        使用白名单方式清理文本，只保留TTS可以正确读出的字符
        
        Args:
            text: 待清理的文本
        
        Returns:
            str: 清理后的文本
        """
        if not text:
            return text
        
        original_text = text
        
        # 先去除末尾的表情符号，避免拆句时产生无意义片段
        text = TextCleaner._remove_trailing_emojis(text)
        # 如果预处理后为空，直接返回
        if not text.strip():
            return ""
        
        # 首先替换特殊符号为可读文本
        text = TextCleaner.replace_special_symbols(text)
        
        # 定义可读字符的正则表达式（白名单）
        # 包括：中文字符、英文字母、数字、基本标点符号、空格等
        readable_pattern = re.compile(r'''
            [
                \u4e00-\u9fff          # 中文汉字
                \u3400-\u4dbf          # 中文扩展A
                \uf900-\ufaff          # 中文兼容汉字
                a-zA-Z                 # 英文字母
                0-9                    # 数字
                \s                     # 空白字符（空格、制表符、换行等）
                
                # 中文标点符号
                ，。！？；：（）、“”
                …                      # 中文省略号
                
                # 英文标点符号
                ,.!?;:'"()\[\]{}
                \.{3}                  # 英文省略号 ...
                
                # 连接符号
                \-_/\\
                
                # 数学运算符号（人类能念出来的）
                \+\-\*×÷=
                
                # 常用符号
                @#%&
                
                # 货币符号（常见的）
                \$￥€£¥¢₹₽₩₪₫₦₡₨₱₵₴₸₺₼₽₾₿
                
                # 度量单位符号
                °℃℉
                
                # 百分号和千分号
                %‰
            ]
        ''', re.VERBOSE)
        
        # 提取所有可读字符
        readable_chars = readable_pattern.findall(text)
        cleaned_text = ''.join(readable_chars)
        
        # 清理多余的空白字符
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text)  # 多个空格合并为一个
        cleaned_text = re.sub(r'\n+', '\n', cleaned_text)  # 多个换行合并为一个
        cleaned_text = cleaned_text.strip()  # 去除首尾空白
        
        # 检查是否有过滤发生
        text_after_symbol_replacement = text.strip()
        final_cleaned_text = cleaned_text.strip()
        
        # 如果符号替换后的文本与最终清理后的文本不同，说明有字符被过滤
        if text_after_symbol_replacement != final_cleaned_text:
            logger.info(f"TTS字符过滤 - 符号替换后: {text_after_symbol_replacement}")
            logger.info(f"TTS字符过滤 - 最终结果: {final_cleaned_text}")
        
        # 检查清理后是否还有可读内容
        if not final_cleaned_text:
            logger.warning(f"文本清理后无可读内容，跳过TTS处理 - 原文: {original_text.strip()}")
            return ""  # 返回空字符串表示跳过TTS
        
        return cleaned_text
    
    @staticmethod
    def should_skip_tts(text: str) -> bool:
        """
        检查文本是否应该跳过TTS处理
        """
        if not text:
            return True
        
        cleaned = TextCleaner.clean_for_tts(text)
        return not cleaned.strip()


class MarkdownCleaner:
    """
    封装 Markdown 清理逻辑：直接用 MarkdownCleaner.clean_markdown(text) 即可
    """
    # 公式字符
    NORMAL_FORMULA_CHARS = re.compile(r'[a-zA-Z\\^_{}\+\-\(\)\[\]=]')

    @staticmethod
    def _replace_inline_dollar(m: re.Match) -> str:
        """
        只要捕获到完整的 "$...$":
          - 如果内部有典型公式字符 => 去掉两侧 $
          - 否则 (纯数字/货币等) => 保留 "$...$"
        """
        content = m.group(1)
        if MarkdownCleaner.NORMAL_FORMULA_CHARS.search(content):
            return content
        else:
            return m.group(0)

    @staticmethod
    def _replace_table_block(match: re.Match) -> str:
        """
        当匹配到一个整段表格块时，回调该函数。
        """
        block_text = match.group('table_block')
        lines = block_text.strip('\n').split('\n')

        parsed_table = []
        for line in lines:
            line_stripped = line.strip()
            if re.match(r'^\|\s*[-:]+\s*(\|\s*[-:]+\s*)+\|?$', line_stripped):
                continue
            columns = [col.strip() for col in line_stripped.split('|') if col.strip() != '']
            if columns:
                parsed_table.append(columns)

        if not parsed_table:
            return ""

        headers = parsed_table[0]
        data_rows = parsed_table[1:] if len(parsed_table) > 1 else []

        lines_for_tts = []
        if len(parsed_table) == 1:
            # 只有一行
            only_line_str = ", ".join(parsed_table[0])
            lines_for_tts.append(f"单行表格：{only_line_str}")
        else:
            lines_for_tts.append(f"表头是：{', '.join(headers)}")
            for i, row in enumerate(data_rows, start=1):
                row_str_list = []
                for col_index, cell_val in enumerate(row):
                    if col_index < len(headers):
                        row_str_list.append(f"{headers[col_index]} = {cell_val}")
                    else:
                        row_str_list.append(cell_val)
                lines_for_tts.append(f"第 {i} 行：{', '.join(row_str_list)}")

        return "\n".join(lines_for_tts) + "\n"

    # 预编译所有正则表达式（按执行频率排序）
    # 这里要把 replace_xxx 的静态方法放在最前定义，以便在列表里能正确引用它们。
    REGEXES = [
        (re.compile(r'```.*?```', re.DOTALL), ''),  # 代码块
        (re.compile(r'^#+\s*', re.MULTILINE), ''),  # 标题
        (re.compile(r'(\*\*|__)(.*?)\1'), r'\2'),  # 粗体
        (re.compile(r'(\*|_)(?=\S)(.*?)(?<=\S)\1'), r'\2'),  # 斜体
        (re.compile(r'!\[.*?\]\(.*?\)'), ''),  # 图片
        (re.compile(r'\[(.*?)\]\(.*?\)'), r'\1'),  # 链接
        (re.compile(r'^\s*>+\s*', re.MULTILINE), ''),  # 引用
        (
            re.compile(r'(?P<table_block>(?:^[^\n]*\|[^\n]*\n)+)', re.MULTILINE),
            _replace_table_block
        ),
        (re.compile(r'^\s*[*+-]\s*', re.MULTILINE), '- '),  # 列表
        (re.compile(r'\$\$.*?\$\$', re.DOTALL), ''),  # 块级公式
        (
            re.compile(r'(?<![A-Za-z0-9])\$([^\n$]+)\$(?![A-Za-z0-9])'),
            _replace_inline_dollar
        ),
        (re.compile(r'\n{2,}'), '\n'),  # 多余空行
    ]

    @staticmethod
    def clean_markdown(text: str) -> str:
        """
        主入口方法：依序执行所有正则，移除或替换 Markdown 元素，并过滤不可读字符
        """
        # 首先清理Markdown格式
        for regex, replacement in MarkdownCleaner.REGEXES:
            text = regex.sub(replacement, text)
        
        # 然后使用白名单过滤，只保留可读字符
        text = TextCleaner.clean_for_tts(text)
        
        return text.strip()