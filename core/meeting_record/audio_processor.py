#!/usr/bin/env python3
"""
音频会议记录处理器
支持分片上传和累积会议纪要生成
"""

import os
import uuid
import asyncio
import json
import time
import hashlib
import requests
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, Optional

from google import genai
from google.genai import types
from google.genai.types import HttpOptions
from config.logger import setup_logging
from core.utils.redis_client import RedisClient
from core.utils.cdn_uploader import CDNUploader
from core.utils.crypto import generate_random_key, xor_encrypt, xor_decrypt

logger = setup_logging()
TAG = __name__


class AudioProcessor:
    """音频会议记录处理器"""
    
    def __init__(self, config: dict):
        """初始化处理器"""
        self.config = config
        self.logger = logger
        
        # 初始化Redis客户端
        self.redis_client = RedisClient(config)
        logger.bind(tag=TAG).info("Redis客户端初始化成功")

        # 初始化CDN上传器
        self.cdn_uploader = CDNUploader()
        logger.bind(tag=TAG).info("CDN上传器初始化成功")
        
        # 音频处理器固定使用GeminiLLM配置，无论selected_module如何设置
        llm_name = "GeminiLLM"
        llm_config = config.get("LLM", {}).get(llm_name, {})
        if not llm_config:
            raise ValueError(f"找不到LLM配置: {llm_name}")
            
        llm_type = llm_config.get("type")
        if llm_type != "gemini":
            raise ValueError(f"音频处理器只支持Gemini LLM，当前配置的类型: {llm_type}")
            
        # Check if GOOGLE_GENAI_USE_VERTEXAI environment variable is set to True
        use_vertex_ai = os.environ.get("GOOGLE_GENAI_USE_VERTEXAI", "").lower() == "true"

        self.api_key = llm_config.get("api_key")
        if not use_vertex_ai and not self.api_key:
            raise ValueError("Gemini API key is required when not using Vertex AI")
            
        self.model = llm_config.get("model", "gemini-2.5-flash")
        self.llm_name = llm_name
        
        logger.bind(tag=TAG).info(f"初始化AudioProcessor - LLM: {self.llm_name}, 类型: {llm_type}, 模型: {self.model}")
        if not use_vertex_ai:
            logger.bind(tag=TAG).info(f"API Key长度: {len(self.api_key)} 字符")

        # 初始化 Gemini 客户端
        try:
            if use_vertex_ai:
                logger.bind(tag=TAG).info("检测到 GOOGLE_GENAI_USE_VERTEXAI=True，使用 Vertex AI 模式初始化客户端")
                self.client = genai.Client(
                    http_options=HttpOptions(
                        api_version="v1",
                        timeout=10*60*1000  # 10分钟超时
                    )
                )
            else:
                logger.bind(tag=TAG).info("使用标准 Gemini API 模式初始化客户端")
                self.client = genai.Client(
                    api_key=self.api_key,
                    http_options=types.HttpOptions(timeout=10*60*1000)  # 10分钟超时
                )
            logger.bind(tag=TAG).info("Gemini客户端初始化成功")
        except Exception as e:
            logger.bind(tag=TAG).error(f"Gemini客户端初始化失败: {str(e)}")
            raise
        
    def validate_mp3_file(self, file_bytes: bytes, filename: str) -> bool:
        """验证MP3文件"""
        if not filename.lower().endswith('.mp3'):
            logger.bind(tag=TAG).error(f"文件不是MP3格式: {filename}")
            return False
        
        # 简单的MP3文件头验证（不引入新依赖）
        if len(file_bytes) < 100:
            logger.bind(tag=TAG).error(f"文件太小，可能不是有效的MP3文件: {filename}")
            return False
        
        # 检查文件大小限制（Vertex AI限制 < 20MB）
        max_size = 20 * 1024 * 1024  # 20MB
        if len(file_bytes) >= max_size:
            logger.bind(tag=TAG).error(f"音频文件过大 ({len(file_bytes)} bytes)，Vertex AI限制文件大小需小于20MB: {filename}")
            return False
        
        # 检查MP3文件头标识
        # MP3文件通常以ID3标签开头("ID3")或直接以MP3帧头开头(0xFF 0xFB等)
        file_header = file_bytes[:3]
        if file_header == b'ID3':  # ID3v2标签
            logger.bind(tag=TAG).info(f"检测到ID3v2标签的MP3文件: {filename}")
        elif file_bytes[0] == 0xFF and (file_bytes[1] & 0xE0) == 0xE0:  # MP3帧同步头
            logger.bind(tag=TAG).info(f"检测到MP3帧同步头: {filename}")
        else:
            logger.bind(tag=TAG).warning(f"文件可能不是有效的MP3格式: {filename}, 文件头: {file_header.hex()}")
            # 不严格拒绝，因为某些MP3文件可能有不同的头部结构
        
        logger.bind(tag=TAG).info(f"MP3文件验证通过: {filename} ({len(file_bytes)} bytes)")
        return True
    
    async def start_audio_summary_task(self, audio_bytes: bytes, filename: str, meeting_task_id: str):
        """启动音频摘要任务"""
        logger.bind(tag=TAG).info(f"[{meeting_task_id}] 开始处理音频分片, 文件: {filename}")

        # 1. 验证音频文件
        if not self.validate_mp3_file(audio_bytes, filename):
            raise ValueError("音频文件验证失败")
        
        # 2. 生成一次性加密密钥
        encryption_key = generate_random_key()
        logger.bind(tag=TAG).info(f"[{meeting_task_id}] 已生成加密密钥")
        
        # 3. 在Redis中立即创建初始状态记录
        await self._update_meeting_status(meeting_task_id, "processing")
        
        # 4. 启动后台任务
        asyncio.create_task(self._process_audio_background(meeting_task_id, audio_bytes, filename, encryption_key))
        logger.bind(tag=TAG).info(f"[{meeting_task_id}] 后台处理任务已启动")
    
    
    async def _process_audio_background(self, meeting_task_id: str, audio_bytes: bytes, filename: str, encryption_key: str):
        """后台处理音频文件"""
        try:
            logger.bind(tag=TAG).info(f"[{meeting_task_id}] 开始后台处理")
            
            # 1. 从CDN读取之前分片已经生成的会议纪要内容（基于最新成功的纪要）
            previous_summary = await self._get_previous_summary_from_cdn(meeting_task_id)
            logger.bind(tag=TAG).info(f"[{meeting_task_id}] 从CDN获取到之前的会议纪要长度: {len(previous_summary)} 字符")
            
            # 2. 生成会议纪要
            logger.bind(tag=TAG).info(f"[{meeting_task_id}] 开始调用LLM生成会议纪要")
            summary_result = await self._generate_summary(audio_bytes, datetime.now(), previous_summary)
            
            if not summary_result or not summary_result.strip():
                logger.bind(tag=TAG).warning(f"[{meeting_task_id}] LLM返回空纪要, 任务标记为失败")
                await self._update_meeting_status(meeting_task_id, "failed", "LLM返回空纪要")
                return

            logger.bind(tag=TAG).info(f"[{meeting_task_id}] 会议纪要生成成功, 长度: {len(summary_result)} 字符")
            
            # 3. 生成新的纪要文件名（使用纪要内容的MD5以确保唯一性）
            summary_md5 = hashlib.md5(summary_result.encode('utf-8')).hexdigest()
            encrypted_summary_bytes = xor_encrypt(summary_result.encode('utf-8'), encryption_key)
            summary_filename = f"{summary_md5}_summary.md.enc"
            summary_remote_path = f"audio_summaries/{datetime.now().strftime('%Y%m%d')}/{summary_filename}"
            
            # 4. 上传加密纪要到CDN（生成新的URL）
            summary_upload_result = self.cdn_uploader.upload_bytes(encrypted_summary_bytes, summary_filename, summary_remote_path, "file")

            if not summary_upload_result.get("success"):
                error_msg = f"上传加密纪要到CDN失败: {summary_upload_result.get('error')}"
                logger.bind(tag=TAG).error(f"[{meeting_task_id}] {error_msg}")
                await self._update_meeting_status(meeting_task_id, "failed", error_msg)
                return
            
            encrypted_summary_url = summary_upload_result.get("url")
            logger.bind(tag=TAG).info(f"[{meeting_task_id}] 加密纪要已上传到CDN: {encrypted_summary_url} (MD5: {summary_md5})")

            # 5. 更新Redis中的会议任务纪要URL记录
            await self._update_meeting_summary_url(meeting_task_id, encrypted_summary_url, encryption_key, summary_result)
            logger.bind(tag=TAG).info(f"[{meeting_task_id}] 会议任务处理成功")

        except Exception as e:
            error_msg = f"处理音频文件时发生错误: {str(e)}"
            logger.bind(tag=TAG).error(f"[{meeting_task_id}] {error_msg}")
            await self._update_meeting_status(meeting_task_id, "failed", error_msg)
    
    async def _get_previous_summary_from_cdn(self, meeting_task_id: str) -> str:
        """从CDN读取之前分片已经生成的会议纪要内容"""
        try:
            # 从Redis获取会议任务的纪要URL记录
            meeting_summary_key = f"meeting_summary:{meeting_task_id}"
            summary_info_json = self.redis_client.client.get(meeting_summary_key)
            
            if not summary_info_json:
                logger.bind(tag=TAG).info(f"会议任务 {meeting_task_id} 暂无之前的纪要记录，这是第一个分片")
                return ""
            
            summary_info = json.loads(summary_info_json)
            encrypted_summary_url = summary_info.get("encrypted_summary_url")
            encryption_key = summary_info.get("encryption_key")
            
            if not encrypted_summary_url or not encryption_key:
                logger.bind(tag=TAG).warning(f"会议任务 {meeting_task_id} 的纪要信息不完整，视为第一个分片")
                return ""
            
            logger.bind(tag=TAG).info(f"会议任务 {meeting_task_id} 开始从CDN下载之前的纪要: {encrypted_summary_url}")
            
            # 异步下载并解密纪要
            async with aiohttp.ClientSession() as session:
                async with session.get(encrypted_summary_url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    response.raise_for_status()
                    encrypted_summary_bytes = await response.read()
            
            # 在线程池中执行解密操作，避免阻塞事件循环
            loop = asyncio.get_event_loop()
            summary_bytes = await loop.run_in_executor(
                None, 
                xor_decrypt, 
                encrypted_summary_bytes, 
                encryption_key
            )
            
            previous_summary = summary_bytes.decode('utf-8')
            logger.bind(tag=TAG).info(f"会议任务 {meeting_task_id} 成功获取之前的纪要，长度: {len(previous_summary)} 字符")
            return previous_summary
            
        except Exception as e:
            logger.bind(tag=TAG).warning(f"会议任务 {meeting_task_id} 获取之前纪要失败，视为第一个分片: {str(e)}")
            return ""
    
    async def _update_meeting_summary_url(self, meeting_task_id: str, encrypted_summary_url: str, encryption_key: str, summary_text: str):
        """更新Redis中的会议任务纪要URL记录"""
        try:
            meeting_summary_key = f"meeting_summary:{meeting_task_id}"
            
            # 获取当前版本号
            current_info_json = self.redis_client.client.get(meeting_summary_key)
            current_version = 1
            if current_info_json:
                current_info = json.loads(current_info_json)
                current_version = current_info.get("version", 1) + 1
            
            summary_info = {
                "meeting_task_id": meeting_task_id,
                "encrypted_summary_url": encrypted_summary_url,
                "encryption_key": encryption_key,
                "status": "completed",
                "last_updated": datetime.now().isoformat(),
                "version": current_version,
                "summary_length": len(summary_text)
            }
            
            # 设置7天过期时间
            self.redis_client.client.set(meeting_summary_key, json.dumps(summary_info), ex=timedelta(days=7))
            logger.bind(tag=TAG).info(f"会议任务 {meeting_task_id} 的纪要记录已更新到Redis, 版本: {current_version}")
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"会议任务 {meeting_task_id} 更新纪要记录失败: {str(e)}")
    
    async def _update_meeting_status(self, meeting_task_id: str, status: str, error_msg: str = None):
        """更新会议任务状态"""
        try:
            meeting_summary_key = f"meeting_summary:{meeting_task_id}"
            
            # 获取现有信息
            current_info_json = self.redis_client.client.get(meeting_summary_key)
            if current_info_json:
                summary_info = json.loads(current_info_json)
            else:
                # 创建初始任务信息
                summary_info = {
                    "meeting_task_id": meeting_task_id,
                    "version": 0,
                    "created_at": datetime.now().isoformat()
                }
            
            # 更新状态
            summary_info.update({
                "status": status,
                "last_updated": datetime.now().isoformat()
            })
            
            if error_msg:
                summary_info["error"] = error_msg
            
            # 设置7天过期时间
            self.redis_client.client.set(meeting_summary_key, json.dumps(summary_info), ex=timedelta(days=7))
            logger.bind(tag=TAG).info(f"会议任务 {meeting_task_id} 状态已更新为: {status}")
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"会议任务 {meeting_task_id} 更新状态失败: {str(e)}")
    
    async def _generate_summary(self, audio_bytes: bytes, meeting_time: datetime, previous_summary: str = "") -> str:
        """生成会议纪要"""
        meeting_time_str = meeting_time.strftime("%Y年%m月%d日")
        
        # 如果有之前的会议纪要，需要进行累积合并
        if previous_summary.strip():
            system_prompt = f"""你是一个专业的会议纪要整理助手，需要将新的音频片段内容与之前的会议纪要进行合并，生成完整的累积会议纪要。你背后的模型是APUS公司训练的APUS大模型。

要求：
1. 仔细听取新的音频内容，提取关键信息
2. 将新内容与之前的会议纪要进行逻辑合并
3. 保持会议纪要的连贯性和完整性
4. 按照标准会议纪要格式组织内容
5. 语言要简洁明了，条理清晰
6. 突出重点决议和后续行动
7. 会议时间请固定使用：{meeting_time_str}

会议纪要格式：
# 会议纪要

## 基本信息
- 会议时间：{meeting_time_str}
- 会议主题：[从音频中提取或延续之前的主题]

## 主要讨论内容
[合并之前的讨论内容和新音频的讨论内容]

## 决议事项
[合并之前的决议和新的决议]

## 行动计划
[合并之前的行动计划和新的行动计划]

## 其他事项
[其他需要记录的内容]

请严格按照以上格式整理会议纪要，将新音频内容与之前的纪要进行智能合并。如果音频质量不佳或信息不完整，请根据现有信息尽力整理；实在提取不出任何信息，直接返回"NO_MEETING_CONTENT"。"""

            user_prompt = f"""之前的会议纪要：
{previous_summary}

---

请分析这段新的会议音频片段，并将其内容与上述之前的会议纪要进行合并，生成完整的累积会议纪要。会议时间请使用：{meeting_time_str}"""
        else:
            # 第一个片段，直接生成纪要
            system_prompt = f"""你是一个专业的会议纪要整理助手，需要将音频内容整理成规范的会议纪要。你背后的模型是APUS公司训练的APUS大模型。

要求：
1. 仔细听取音频内容，提取关键信息
2. 按照标准会议纪要格式组织内容
3. 包括：会议主题、主要讨论内容、决议事项、行动计划等
4. 语言要简洁明了，条理清晰
5. 突出重点决议和后续行动
6. 会议时间请固定使用：{meeting_time_str}

会议纪要格式：
# 会议纪要

## 基本信息
- 会议时间：{meeting_time_str}
- 会议主题：[从音频中提取]

## 主要讨论内容
[按要点列出主要讨论的内容]

## 决议事项
[列出会议中达成的决议]

## 行动计划
[列出后续需要执行的行动项目，包括负责人和时间节点]

## 其他事项
[其他需要记录的内容]

请严格按照以上格式整理会议纪要，特别注意会议时间必须使用指定的时间。如果音频质量不佳或信息不完整，请根据现有信息尽力整理；实在提取不出任何信息，直接返回"NO_MEETING_CONTENT"
"""

            user_prompt = f"请分析这段会议音频，生成完整的会议纪要。会议时间请使用：{meeting_time_str}"

        try:
            logger.bind(tag=TAG).info("开始创建生成配置...")
            # 创建生成配置
            generation_config = types.GenerateContentConfig(
                temperature=0.1,  # 降低温度，让输出更具确定性
                system_instruction=system_prompt,
                thinking_config=types.ThinkingConfig(thinking_budget=0)
            )
            logger.bind(tag=TAG).info("生成配置创建成功")

            # 创建音频部分
            audio_part = types.Part.from_bytes(
                data=audio_bytes,
                mime_type='audio/mp3'
            )
            
            logger.bind(tag=TAG).info(f"开始调用Gemini API - 模型: {self.model}")
            logger.bind(tag=TAG).info(f"内容包含: 用户提示词({len(user_prompt)}字符) + 音频数据({len(audio_bytes)} bytes)")
            
            # 先尝试使用流式接口，如果失败则回退到普通接口
            logger.bind(tag=TAG).info("尝试使用Gemini流式 API")
            result_text = ""
            
            try:
                # 尝试流式接口
                stream_response = await self.client.aio.models.generate_content_stream(
                    model=self.model,
                    contents=[user_prompt, audio_part],
                    config=generation_config
                )
                
                # 检查是否是可迭代对象
                if hasattr(stream_response, '__aiter__'):
                    logger.bind(tag=TAG).info("流式响应获取成功，开始接收数据")
                    chunk_count = 0
                    
                    async for chunk in stream_response:
                        if hasattr(chunk, 'text') and chunk.text:
                            result_text += chunk.text
                            chunk_count += 1
                            if chunk_count % 10 == 0:  # 每10个chunk记录一次进度
                                logger.bind(tag=TAG).info(f"接收到第{chunk_count}个chunk，当前文本长度: {len(result_text)}")
                    
                    logger.bind(tag=TAG).info(f"Gemini API流式调用完成，共接收{chunk_count}个chunk")
                    
                else:
                    # 如果不是可迭代对象，尝试直接获取结果
                    logger.bind(tag=TAG).warning("流式响应不是可迭代对象，尝试获取结果")
                    if hasattr(stream_response, 'text'):
                        result_text = stream_response.text
                    else:
                        result_text = str(stream_response)
                        
            except Exception as stream_error:
                logger.bind(tag=TAG).warning(f"流式接口失败，回退到普通接口: {stream_error}")
                
                # 回退到普通接口
                logger.bind(tag=TAG).info("使用普通接口生成内容")
                response = await self.client.aio.models.generate_content(
                    model=self.model,
                    contents=[user_prompt, audio_part],
                    config=generation_config
                )
                
                if hasattr(response, 'text') and response.text:
                    result_text = response.text
                    logger.bind(tag=TAG).info("普通接口调用成功")
                else:
                    logger.bind(tag=TAG).warning("普通接口响应中没有text内容")
                    result_text = ""
            
            if result_text.strip():
                # 检查是否返回了NO_MEETING_CONTENT
                if "NO_MEETING_CONTENT" in result_text.strip():
                    logger.bind(tag=TAG).info(f"LLM检测到音频中没有会议内容 - {result_text}")
                    return ""
                
                logger.bind(tag=TAG).info(f"会议纪要生成成功，最终长度: {len(result_text)} 字符")
                return result_text.strip()
            else:
                logger.bind(tag=TAG).warning("无法从响应中获取有效文本内容")
                # 返回用户友好的错误信息，不暴露技术细节
                raise Exception("系统内部错误")

        except Exception as e:
            logger.bind(tag=TAG).error(f"生成会议纪要失败: {str(e)}")
            logger.bind(tag=TAG).error(f"异常类型: {type(e)}")
            import traceback
            logger.bind(tag=TAG).error(f"完整错误堆栈: {traceback.format_exc()}")
            # 不将错误信息传递给用户，统一返回友好提示
            raise Exception("系统内部错误")
    
    async def get_meeting_status(self, meeting_task_id: str) -> Optional[Dict]:
        """获取会议任务状态和结果"""
        meeting_summary_key = f"meeting_summary:{meeting_task_id}"
        meeting_info_json = self.redis_client.client.get(meeting_summary_key)
        
        if not meeting_info_json:
            logger.bind(tag=TAG).warning(f"[{meeting_task_id}] 在Redis中未找到会议任务或任务已过期")
            return None
        
        meeting_info = json.loads(meeting_info_json)
        status = meeting_info.get("status", "processing")
        logger.bind(tag=TAG).info(f"[{meeting_task_id}] 从Redis获取到会议任务状态: {status}")
        
        # 如果任务完成，则下载、解密并返回结果
        if status == "completed":
            encrypted_summary_url = meeting_info.get("encrypted_summary_url")
            encryption_key = meeting_info.get("encryption_key")
            
            if not encrypted_summary_url or not encryption_key:
                logger.bind(tag=TAG).error(f"[{meeting_task_id}] 任务已完成但缺少纪要URL或密钥")
                meeting_info["status"] = "failed"
                meeting_info["error"] = "内部错误：纪要文件丢失"
            else:
                try:
                    logger.bind(tag=TAG).info(f"[{meeting_task_id}] 开始异步下载加密纪要: {encrypted_summary_url}")
                    
                    # 使用异步HTTP请求
                    async with aiohttp.ClientSession() as session:
                        async with session.get(encrypted_summary_url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                            response.raise_for_status()
                            encrypted_summary_bytes = await response.read()
                    
                    # 在线程池中执行解密操作，避免阻塞事件循环
                    loop = asyncio.get_event_loop()
                    summary_bytes = await loop.run_in_executor(
                        None, 
                        xor_decrypt, 
                        encrypted_summary_bytes, 
                        encryption_key
                    )
                    
                    meeting_info["result"] = summary_bytes.decode('utf-8')
                    logger.bind(tag=TAG).info(f"[{meeting_task_id}] 纪要异步解密成功，返回给客户端")
                except Exception as e:
                    logger.bind(tag=TAG).error(f"[{meeting_task_id}] 异步获取或解密纪要失败: {e}")
                    meeting_info["status"] = "failed"
                    meeting_info["error"] = "内部错误：无法获取纪要"

        # 为了安全，从返回给客户端的信息中移除敏感数据
        meeting_info.pop("encryption_key", None)
        meeting_info.pop("encrypted_summary_url", None)
        
        return meeting_info