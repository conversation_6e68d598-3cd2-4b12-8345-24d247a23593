import asyncio
import json
import os
import time
import uuid
from datetime import datetime
from aiohttp import web
from aiohttp.web_response import Response
from config.logger import setup_logging

from core.utils.redis_client import RedisClient
from core.utils.llm import create_instance
from core.auth import AuthMiddleware, AuthenticationError
from core.policy_check import PolicyCheck
from core.utils.cdn_uploader import CDNUploader
from core.meeting_record.audio_processor import AudioProcessor

TAG = __name__


class HttpApiServer:
    def __init__(self, config: dict, websocket_server=None):
        self.config = config
        self.websocket_server = websocket_server
        self.logger = setup_logging()
        self.start_time = time.time()

        # 初始化认证中间件
        self.auth = AuthMiddleware(config)

        # 初始化Redis客户端
        self.redis_client = RedisClient(config) if config.get("server", {}).get("redis", {}).get("enabled", False) else None

        # 初始化LLM
        self._init_llm()
        
        # 初始化内容安全检测
        self.policy_check = PolicyCheck(config)
        
        # 初始化CDN上传器
        self.cdn_uploader = CDNUploader()
        
        # 初始化音频处理器
        try:
            self.audio_processor = AudioProcessor(config)
            self.logger.bind(tag=TAG).info("音频处理器初始化成功")
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"音频处理器初始化失败: {e}")
            self.audio_processor = None

    async def _authenticate_request(self, request: web.Request) -> bool:
        """验证HTTP请求的认证信息"""
        try:
            # 从HTTP headers中提取认证信息
            headers = {
                'client-id': request.headers.get('client-id', ''),
                'device-id': request.headers.get('device-id', ''),
                'authorization': request.headers.get('authorization', '')
            }
            
            # 使用AuthMiddleware进行认证
            await self.auth.authenticate(headers)
            
            # 认证成功，记录请求信息
            client_id = headers.get('client-id', 'unknown')
            device_id = headers.get('device-id', 'unknown')
            self.logger.bind(tag=TAG).info(f"HTTP API认证成功 - Path: {request.path}, Client: {client_id}, Device: {device_id}")
            return True
            
        except AuthenticationError as e:
            self.logger.bind(tag=TAG).warning(f"HTTP API认证失败 - Path: {request.path}, Error: {e}")
            return False
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"HTTP API认证过程中发生错误 - Path: {request.path}, Error: {e}")
            return False

    def _create_auth_error_response(self, message: str = "认证失败") -> Response:
        """创建认证失败的响应"""
        response = web.json_response({
            "success": False,
            "message": message,
            "error_code": "AUTHENTICATION_FAILED",
            "timestamp": datetime.now().isoformat()
        })
        return self._add_cors_headers(response)

    def _init_llm(self):
        """初始化LLM实例"""
        try:
            selected_module = self.config.get("selected_module", {})
            llm_name = selected_module.get("LLM")
            if llm_name:
                llm_config = self.config.get("LLM", {}).get(llm_name, {})
                if llm_config:
                    llm_type = llm_config.get("type")
                    if llm_type:
                        self.llm = create_instance(llm_type, llm_config)
                        self.logger.bind(tag=TAG).info(f"LLM初始化成功: {llm_name} ({llm_type})")
                    else:
                        self.logger.bind(tag=TAG).warning(f"LLM配置中缺少type字段: {llm_name}")
                        self.llm = None
                else:
                    self.logger.bind(tag=TAG).warning(f"找不到LLM配置: {llm_name}")
                    self.llm = None
            else:
                self.logger.bind(tag=TAG).warning("未配置LLM模块")
                self.llm = None
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"LLM初始化失败: {e}")
            self.llm = None

    async def start(self):
        """启动HTTP API服务器"""
        server_config = self.config["server"]
        host = server_config.get("ip", "0.0.0.0")
        port = int(server_config.get("http_api_port", 8100))

        app = web.Application()

        # 添加路由
        self._setup_routes(app)

        # 运行服务
        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, host, port)
        await site.start()

        self.logger.bind(tag=TAG).info(f"HTTP API服务器启动成功，监听端口: {port}")

        # 【重要修复】添加取消信号避免无限轮询导致CPU占用
        self._stop_event = asyncio.Event()

        # 保持服务运行
        try:
            while True:
                try:
                    # 【修复】使用带超时的等待，支持优雅关闭
                    await asyncio.wait_for(self._stop_event.wait(), timeout=3600)
                    self.logger.bind(tag=TAG).info("HTTP API服务器收到停止信号")
                    break
                except asyncio.TimeoutError:
                    # 超时是正常的，继续等待
                    continue
        except asyncio.CancelledError:
            self.logger.bind(tag=TAG).info("HTTP API服务器被取消")
            raise
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"HTTP API服务器异常: {e}")
            raise

    def _add_cors_headers(self, response):
        """添加CORS头"""
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, client-id, device-id'
        return response

    def _setup_routes(self, app: web.Application):
        """设置API路由"""
        # 主要功能路由
        app.router.add_post('/api/v1/set_user_profile', self._handle_set_user_profile)
        app.router.add_post('/api/v1/text_summary', self._handle_text_summary)
        app.router.add_post('/api/v1/upload_avatar', self._handle_upload_avatar)
        app.router.add_post('/api/v1/upload_nickname', self._handle_upload_nickname)
        app.router.add_post('/api/v1/audio_summary', self._handle_audio_summary)
        app.router.add_get('/api/v1/audio_summary/{task_id}', self._handle_get_audio_summary)

        # OPTIONS请求处理（用于CORS预检）
        app.router.add_options('/{path:.*}', self._handle_options)

        # 健康检查端点（Docker容器必需）
        app.router.add_get('/health', self._handle_health_check)

        # 根路径
        app.router.add_get('/', self._handle_root)

    async def _handle_health_check(self, request: web.Request) -> Response:
        """健康检查端点"""
        try:
            # 检查基本服务状态
            health_status = {
                "status": "healthy",
                "service": "http-api",
                "timestamp": datetime.now().isoformat(),
                "uptime": time.time() - self.start_time,
                "checks": {}
            }
            
            # 检查Redis连接（如果启用）
            if self.redis_client and self.redis_client.enabled:
                try:
                    self.redis_client.client.ping()
                    health_status["checks"]["redis"] = "healthy"
                except Exception as e:
                    health_status["checks"]["redis"] = f"unhealthy: {str(e)}"
                    health_status["status"] = "degraded"
            else:
                health_status["checks"]["redis"] = "disabled"
            
            # 检查LLM服务
            if self.llm:
                health_status["checks"]["llm"] = "healthy"
            else:
                health_status["checks"]["llm"] = "unavailable"
                health_status["status"] = "degraded"
            
            # 根据检查结果确定HTTP状态码（Docker需要）
            status_code = 200 if health_status["status"] == "healthy" else 503
            response = web.json_response(health_status, status=status_code)
            return self._add_cors_headers(response)
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"健康检查失败: {e}")
            health_status = {
                "status": "unhealthy",
                "service": "http-api",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
            response = web.json_response(health_status, status=503)
            return self._add_cors_headers(response)

    async def _handle_options(self, request: web.Request) -> Response:
        """处理OPTIONS预检请求"""
        response = web.Response(status=200)
        return self._add_cors_headers(response)

    async def _handle_root(self, request: web.Request) -> Response:
        """处理根路径请求"""
        response = web.json_response({
            "message": "YuYan HTTP API Server",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "authentication": {
                "required_headers": ["client-id", "authorization"],
                "authorization_format": "Bearer <token>",
                "note": "所有API端点都需要认证"
            },
            "endpoints": [
                "POST /api/v1/set_user_profile - 设置用户档案并生成个性化问候语",
                "POST /api/v1/text_summary - 文本摘要生成会议纪要",
                "POST /api/v1/upload_avatar - 上传用户头像并进行违规检测",
                "POST /api/v1/upload_nickname - 上传用户昵称并进行违规检测",
                "POST /api/v1/audio_summary - 上传MP3音频文件生成会议纪要（异步）",
                "GET /api/v1/audio_summary/{task_id} - 查询音频摘要任务状态和结果"
            ]
        })
        return self._add_cors_headers(response)

    async def _handle_set_user_profile(self, request: web.Request) -> Response:
        """设置用户档案并生成个性化问候语"""
        try:
            # 验证认证信息
            if not await self._authenticate_request(request):
                return self._create_auth_error_response("认证失败，请检查client-id和authorization头信息")
                
            # 解析请求数据
            data = await request.json()

            # 记录客户端请求数据
            self.logger.bind(tag=TAG).info(f"[用户档案接口] 收到客户端请求: {json.dumps(data, ensure_ascii=False)}")

            # 验证必要字段
            if not data or not isinstance(data, dict):
                response = web.json_response({
                    "success": False,
                    "message": "请求数据格式错误，需要JSON格式"
                })
                return self._add_cors_headers(response)

            # 获取用户信息
            user_name = data.get("user_name", "")
            user_gender = data.get("user_gender", "")  # 用户的性别
            user_birth = data.get("user_birth", "")    # 用户的生日
            lifestyle = data.get("lifestyle", "")
            interests = data.get("interests", "")
            client_id = request.headers.get('client-id', '')

            # 获取AI相关信息
            ai_gender = data.get("ai_gender", "")  # 用户希望的AI性别：男/女
            ai_name = data.get("ai_name", "")      # 用户希望给AI起的名字

            # 容错处理：user_name可以为空，不再强制要求

            # 保存用户数据到Redis并设置AI角色
            if self.redis_client and self.redis_client.enabled and client_id:
                try:
                    # 根据AI性别确定角色
                    ai_role = self._determine_ai_role(ai_gender)

                    # 获取现有用户数据
                    user_data = self.redis_client.get_user_data(client_id) or {}

                    # 容错处理：只有当user_name不为空时才保存该字段
                    if user_name.strip():
                        user_data["user_name"] = user_name

                    # 构建用户档案和偏好信息，兼容memory模块的schema
                    profile_data = {}
                    preferences_data = {}
                    
                    # 构建用户档案信息
                    if user_name.strip():
                        profile_data["name"] = user_name
                    if user_gender:
                        profile_data["gender"] = user_gender
                    if user_birth:
                        profile_data["important_dates"] = user_birth
                    if lifestyle:
                        profile_data["lifestyle"] = lifestyle
                    if interests:
                        # interests可能是字符串或列表，统一处理为列表
                        if isinstance(interests, str):
                            interests_list = [item.strip() for item in interests.split(',') if item.strip()]
                        else:
                            interests_list = interests if isinstance(interests, list) else [str(interests)]
                        profile_data["interests"] = interests_list
                    
                    # 构建偏好信息（可以通过其他接口扩展）
                    # preferences_data["communication_style"] = ""
                    # preferences_data["special_requests"] = ""
                    
                    # 更新用户数据
                    user_data.update({
                        "user_gender": user_gender,
                        "user_birth": user_birth,
                        "lifestyle": lifestyle,
                        "interests": interests,
                    })
                    
                    # 保存兼容memory模块的profile和preferences
                    if profile_data:
                        user_data["profile"] = profile_data
                        user_data["profile_last_update"] = time.time()
                    if preferences_data:
                        user_data["preferences"] = preferences_data

                    # 如果设置了AI角色，保存到用户数据中
                    if ai_role:
                        user_data["role"] = ai_role
                        self.logger.bind(tag=TAG).info(f"[用户档案接口] 根据AI性别'{ai_gender}'设置角色为: {ai_role}")

                    # 如果设置了AI名字，保存为assistant_name
                    if ai_name:
                        user_data["assistant_name"] = ai_name
                        self.logger.bind(tag=TAG).info(f"[用户档案接口] 设置AI助手名字为: {ai_name}")

                    # 保存用户数据
                    self.redis_client.save_user_data(client_id, user_data)

                    # 记录存储到Redis的原始JSON数据
                    self.logger.bind(tag=TAG).info(f"[用户档案接口] 往用户记忆存储的数据 - client_id: {client_id}, 原始JSON: {json.dumps(user_data, ensure_ascii=False)}")
                except Exception as e:
                    self.logger.bind(tag=TAG).error(f"[用户档案接口] 保存用户数据到Redis失败: {e}")

            # 生成个性化问候语（处理user_name为空的情况）
            greeting = await self._generate_personalized_greeting(user_name, user_gender, user_birth, lifestyle, interests, ai_gender, ai_name)

            # 获取设置的角色信息
            ai_role = self._determine_ai_role(ai_gender)

            response_data = {
                "success": True,
                "message": "用户档案设置成功",
                "data": {
                    "user_name": user_name if user_name.strip() else None,  # 容错处理：空字符串返回null
                    "user_gender": user_gender,
                    "user_birth": user_birth,
                    "lifestyle": lifestyle,
                    "interests": interests,
                    "ai_gender": ai_gender,
                    "ai_name": ai_name,
                    "ai_role": ai_role,
                    "personalized_greeting": greeting
                },
                "timestamp": datetime.now().isoformat()
            }

            # 记录最终响应数据
            self.logger.bind(tag=TAG).info(f"[用户档案接口] 返回给客户端的响应: {json.dumps(response_data, ensure_ascii=False)}")

            response = web.json_response(response_data)
            return self._add_cors_headers(response)

        except json.JSONDecodeError:
            response = web.json_response({
                "success": False,
                "message": "JSON格式错误"
            })
            return self._add_cors_headers(response)
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"设置用户档案时发生错误: {e}")
            response = web.json_response({
                "success": False,
                "message": f"服务器内部错误: {str(e)}"
            })
            return self._add_cors_headers(response)

    async def _handle_text_summary(self, request: web.Request) -> Response:
        """文本摘要生成会议纪要"""
        try:
            # 验证认证信息
            if not await self._authenticate_request(request):
                return self._create_auth_error_response("认证失败，请检查client-id和authorization头信息")
                
            # 解析请求数据
            data = await request.json()

            # 记录客户端请求数据
            self.logger.bind(tag=TAG).info(f"[文本摘要接口] 收到客户端请求: {json.dumps(data, ensure_ascii=False)}")

            # 验证必要字段
            if not data or not isinstance(data, dict):
                response = web.json_response({
                    "success": False,
                    "message": "请求数据格式错误，需要JSON格式"
                })
                return self._add_cors_headers(response)

            text_content = data.get("text", "")
            if not text_content or not text_content.strip():
                response = web.json_response({
                    "success": False,
                    "message": "文本内容不能为空"
                })
                return self._add_cors_headers(response)

            # 生成会议纪要
            summary = await self._generate_meeting_summary(text_content.strip())

            response_data = {
                "success": True,
                "message": "会议纪要生成成功",
                "data": {
                    "original_text_length": len(text_content),
                    "summary": summary
                },
                "timestamp": datetime.now().isoformat()
            }

            # 记录最终响应数据
            self.logger.bind(tag=TAG).info(f"[文本摘要接口] 返回给客户端的响应: {json.dumps(response_data, ensure_ascii=False)}")

            response = web.json_response(response_data)
            return self._add_cors_headers(response)

        except json.JSONDecodeError:
            response = web.json_response({
                "success": False,
                "message": "JSON格式错误"
            })
            return self._add_cors_headers(response)
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"生成文本摘要时发生错误: {e}")
            response = web.json_response({
                "success": False,
                "message": f"服务器内部错误: {str(e)}"
            })
            return self._add_cors_headers(response)

    async def _generate_personalized_greeting(self, user_name: str, user_gender: str, user_birth: str, lifestyle: str, interests: str, ai_gender: str = "", ai_name: str = "") -> str:
        """生成个性化问候语"""
        try:
            if not self.llm:
                if user_name and user_name.strip():
                    return f"嗨，{user_name}！很高兴认识你！"
                else:
                    return "嗨！很高兴认识你！"

            # 构建系统提示词，考虑AI性别和名字
            gender_context = self._get_gender_context(ai_gender)

            system_prompt = f"""你是一个友善的AI助手{gender_context}，需要根据用户的信息生成个性化的问候语。

要求：
1. 语气要亲切友好，充满好奇心
2. 要体现对用户生活方式和兴趣爱好的关注
3. 要表达期待进一步了解用户的愿望
4. 语言要自然流畅，像朋友间的对话
5. 长度控制在100字以内
6. 如果用户给AI起了名字，要在问候语中自然地提及这个名字
7. 要体现出AI的性别特征（如果用户指定了性别）

请根据用户信息生成一段个性化问候语，注意符合自己的性别：
1、引用用户的生活方式和兴趣爱好，表达对用户的关注
2、邀请用户进一步交流"""

            # 构建用户提示词
            user_name_display = user_name if user_name and user_name.strip() else '未提供'
            user_prompt = f"""用户介绍的信息：
- 昵称：{user_name_display}
- 性别：{user_gender if user_gender else '未提供'}
- 生活方式：{lifestyle if lifestyle else '未提供'}
- 兴趣爱好：{interests if interests else '未提供'}
- 希望AI的性别：{ai_gender if ai_gender else '未指定'}
- 给AI起的名字：{ai_name if ai_name else '未指定'}

请生成个性化问候语："""

            # 记录传给LLM的数据
            self.logger.bind(tag=TAG).info(f"[个性化问候语] 传给LLM的系统提示词: {system_prompt}")
            self.logger.bind(tag=TAG).info(f"[个性化问候语] 传给LLM的用户提示词: {user_prompt}")

            # 调用LLM生成问候语
            greeting = self.llm.response_no_stream(system_prompt, user_prompt)

            # 记录LLM返回的数据
            self.logger.bind(tag=TAG).info(f"[个性化问候语] LLM返回的原始结果: {greeting}")

            if greeting and greeting.strip():
                final_greeting = greeting.strip()
                self.logger.bind(tag=TAG).info(f"[个性化问候语] 为用户 {user_name} 生成个性化问候语成功，最终结果: {final_greeting}")
                return final_greeting
            else:
                if user_name and user_name.strip():
                    fallback_greeting = f"嗨，{user_name}！很高兴认识你！期待与你的交流！"
                else:
                    fallback_greeting = "嗨！很高兴认识你！期待与你的交流！"
                self.logger.bind(tag=TAG).warning(f"[个性化问候语] LLM返回空结果，使用默认问候语: {fallback_greeting}")
                return fallback_greeting

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"生成个性化问候语失败: {e}")
            if user_name and user_name.strip():
                return f"嗨，{user_name}！很高兴认识你！"
            else:
                return "嗨！很高兴认识你！"

    async def _generate_meeting_summary(self, text_content: str) -> str:
        """生成会议纪要"""
        try:
            if not self.llm:
                return "抱歉，LLM服务不可用，无法生成会议纪要。"

            # 获取服务器当前时间
            import datetime
            current_time = datetime.datetime.now().strftime("%Y年%m月%d日 %H:%M")

            # 构建系统提示词
            system_prompt = f"""你是一个专业的会议纪要整理助手，需要将用户提供的文本内容整理成规范的会议纪要。

要求：
1. 提取关键信息，包括：会议主题、主要讨论内容、决议事项、行动计划等
2. 按照标准会议纪要格式组织内容
3. 语言要简洁明了，条理清晰
4. 突出重点决议和后续行动
5. 如果原文信息不完整，请根据现有信息尽力整理

会议纪要格式参考：
# 会议纪要

## 基本信息
- 会议时间：{current_time}
- 会议主题：[从文本中提取]

## 主要讨论内容
[按要点列出主要讨论的内容]

## 决议事项
[列出会议中达成的决议]

## 行动计划
[列出后续需要执行的行动项目，包括负责人和时间节点]

## 其他事项
[其他需要记录的内容]

请严格按照以上格式整理会议纪要。"""

            # 构建用户提示词
            user_prompt = f"请将以下文本内容整理成会议纪要：\n\n{text_content}"

            # 记录传给LLM的数据
            self.logger.bind(tag=TAG).info(f"[会议纪要] 传给LLM的系统提示词: {system_prompt}")
            self.logger.bind(tag=TAG).info(f"[会议纪要] 传给LLM的用户提示词: {user_prompt}")

            # 调用LLM生成会议纪要
            summary = self.llm.response_no_stream(system_prompt, user_prompt)

            # 记录LLM返回的数据
            self.logger.bind(tag=TAG).info(f"[会议纪要] LLM返回的原始结果: {summary}")

            if summary and summary.strip():
                final_summary = summary.strip()
                self.logger.bind(tag=TAG).info(f"[会议纪要] 生成成功，最终结果: {final_summary}")
                return final_summary
            else:
                fallback_summary = "抱歉，无法生成会议纪要，请检查输入内容。"
                self.logger.bind(tag=TAG).warning(f"[会议纪要] LLM返回空结果，使用默认回复: {fallback_summary}")
                return fallback_summary

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"生成会议纪要失败: {e}")
            return f"生成会议纪要时发生错误：{str(e)}"

    def _get_gender_context(self, ai_gender: str) -> str:
        """获取性别上下文"""
        if ai_gender:
            if ai_gender.lower() in ['女', 'female', '女性']:
                return "（女性）"
            elif ai_gender.lower() in ['男', 'male', '男性']:
                return "（男性）"
        return ""

    def _determine_ai_role(self, ai_gender: str) -> str:
        """根据AI性别确定对应的角色

        Args:
            ai_gender: 用户选择的AI性别

        Returns:
            str: 对应的角色名称，如果性别无效则返回空字符串
        """
        if not ai_gender:
            return ""

        if ai_gender.lower() in ['男', 'male', '男性']:
            return "治愈男闺蜜"
        elif ai_gender.lower() in ['女', 'female', '女性']:
            return "俏皮女闺蜜"
        else:
            self.logger.bind(tag=TAG).warning(f"未识别的AI性别: {ai_gender}")
            return ""

    async def _handle_upload_avatar(self, request: web.Request) -> Response:
        """上传用户头像文件并进行违规检测"""
        try:
            # 验证认证信息
            if not await self._authenticate_request(request):
                return self._create_auth_error_response("认证失败，请检查client-id和authorization头信息")
            
            client_id = request.headers.get('client-id', '')
            
            # 检查请求是否包含文件
            if not request.content_type or not request.content_type.startswith('multipart/form-data'):
                response = web.json_response({
                    "success": False,
                    "message": "请求必须是multipart/form-data格式"
                })
                return self._add_cors_headers(response)
            
            # 读取上传的文件
            reader = await request.multipart()
            avatar_file = None
            filename = None
            
            async for field in reader:
                if field.name == 'avatar' and field.filename:
                    filename = field.filename
                    avatar_file = await field.read()
                    break
            
            if not avatar_file or not filename:
                response = web.json_response({
                    "success": False,
                    "message": "未找到头像文件，请确保字段名为'avatar'"
                })
                return self._add_cors_headers(response)
            
            # 验证文件类型
            allowed_extensions = {'.jpg', '.jpeg', '.png', '.webp', '.gif', '.tiff', '.tif', '.heif'}
            file_ext = os.path.splitext(filename)[1].lower()
            
            if file_ext not in allowed_extensions:
                response = web.json_response({
                    "success": False,
                    "message": f"不支持的文件格式: {file_ext}，支持的格式: {', '.join(allowed_extensions)}"
                })
                return self._add_cors_headers(response)
            
            # 验证文件大小 (限制为5MB)
            max_size = 5 * 1024 * 1024  # 5MB
            if len(avatar_file) > max_size:
                response = web.json_response({
                    "success": False,
                    "message": f"文件大小超过限制，最大允许5MB，当前文件大小: {len(avatar_file)/1024/1024:.2f}MB"
                })
                return self._add_cors_headers(response)
            
            self.logger.bind(tag=TAG).info(f"[头像上传接口] 收到文件: {filename}, 大小: {len(avatar_file)} bytes")
            
            # 上传文件到CDN
            upload_result = self.cdn_uploader.upload_bytes(
                file_bytes=avatar_file,
                filename=filename,
                remote_path=f"avatars/{client_id}/{int(time.time())}_{filename}",
                file_type="image"
            )
            
            # 详细记录CDN上传结果
            self.logger.bind(tag=TAG).info(f"[头像上传接口] CDN上传完整结果: {json.dumps(upload_result, ensure_ascii=False)}")
            
            if not upload_result.get("success", False):
                response = web.json_response({
                    "success": False,
                    "message": f"文件上传到CDN失败: {upload_result.get('error', '未知错误')}"
                })
                return self._add_cors_headers(response)
            
            # 从CDN结果中提取URL
            avatar_url = upload_result.get("url", "")
            cdn_data = upload_result.get("data", {})
            
            # 如果url为空，尝试从data中获取
            if not avatar_url and isinstance(cdn_data, dict):
                # 尝试不同的可能字段名
                avatar_url = cdn_data.get("url") or cdn_data.get("imageUrl") or cdn_data.get("downloadUrl") or ""
                
                # 尝试从嵌套的data结构中获取（基于实际CDN响应结构）
                if not avatar_url and "data" in cdn_data and isinstance(cdn_data["data"], dict):
                    nested_data = cdn_data["data"]
                    # 优先使用publicURL，其次使用host+url拼接
                    avatar_url = nested_data.get("publicURL")
                    if not avatar_url:
                        host = nested_data.get("host", "")
                        relative_url = nested_data.get("url", "")
                        if host and relative_url:
                            avatar_url = f"{host}/{relative_url}"
            
            self.logger.bind(tag=TAG).info(f"[头像上传接口] 最终提取的avatar_url: {avatar_url}")
            self.logger.bind(tag=TAG).info(f"[头像上传接口] CDN返回的data字段: {json.dumps(cdn_data, ensure_ascii=False)}")
            
            # 进行头像违规检测
            check_result = await self.policy_check.check_user_avatar(avatar_url, client_id)
            
            # 记录检测结果
            self.logger.bind(tag=TAG).info(f"[头像上传接口] 违规检测结果: {json.dumps(check_result, ensure_ascii=False)}")
            
            # 如果检测被拦截，返回错误信息
            if check_result.get("is_blocked", False):
                response_data = {
                    "success": False,
                    "message": "头像内容违规，请更换符合规范的头像",
                    "data": {
                        "avatar_url": avatar_url,
                        "is_blocked": True,
                        "detect_result": check_result.get("detect_result"),
                        "label_code": check_result.get("label_code"),
                        "violation_reason": self._get_violation_reason(check_result.get("label_code")),
                        "upload_info": {
                            "filename": filename,
                            "file_size": len(avatar_file)
                        }
                    },
                    "timestamp": datetime.now().isoformat()
                }
                
                self.logger.bind(tag=TAG).warning(f"[头像上传接口] 头像违规被拦截: {avatar_url}")
                response = web.json_response(response_data, status=200)
                return self._add_cors_headers(response)
            
            # 头像检测通过，只返回结果不保存到Redis
            self.logger.bind(tag=TAG).info(f"[头像上传接口] 头像检测通过，不保存到Redis - client_id: {client_id}")

            response_data = {
                "success": True,
                "message": "头像上传成功",
                "data": {
                    "avatar_url": avatar_url,
                    "is_blocked": False,
                    "detect_result": check_result.get("detect_result", "PASS"),
                    "upload_info": {
                        "filename": filename,
                        "file_size": len(avatar_file)
                    }
                },
                "timestamp": datetime.now().isoformat()
            }

            # 记录最终响应数据
            self.logger.bind(tag=TAG).info(f"[头像上传接口] 返回给客户端的响应: {json.dumps(response_data, ensure_ascii=False)}")

            response = web.json_response(response_data)
            return self._add_cors_headers(response)

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"上传头像时发生错误: {e}")
            response = web.json_response({
                "success": False,
                "message": f"服务器内部错误: {str(e)}"
            })
            return self._add_cors_headers(response)

    async def _handle_upload_nickname(self, request: web.Request) -> Response:
        """上传用户昵称并进行违规检测"""
        try:
            # 验证认证信息
            if not await self._authenticate_request(request):
                return self._create_auth_error_response("认证失败，请检查client-id和authorization头信息")
                
            # 解析请求数据
            data = await request.json()

            # 记录客户端请求数据
            self.logger.bind(tag=TAG).info(f"[昵称上传接口] 收到客户端请求: {json.dumps(data, ensure_ascii=False)}")

            # 验证必要字段
            if not data or not isinstance(data, dict):
                response = web.json_response({
                    "success": False,
                    "message": "请求数据格式错误，需要JSON格式"
                })
                return self._add_cors_headers(response)

            nickname = data.get("nickname", "")
            if not nickname or not nickname.strip():
                response = web.json_response({
                    "success": False,
                    "message": "昵称不能为空"
                })
                return self._add_cors_headers(response)

            client_id = request.headers.get('client-id', '')
            
            # 进行昵称违规检测
            check_result = await self.policy_check.check_user_nickname(nickname.strip(), client_id)
            
            # 记录检测结果
            self.logger.bind(tag=TAG).info(f"[昵称上传接口] 违规检测结果: {json.dumps(check_result, ensure_ascii=False)}")
            
            # 如果检测被拦截，返回错误信息
            if check_result.get("is_blocked", False):
                response_data = {
                    "success": False,
                    "message": "昵称内容违规，请更换符合规范的昵称",
                    "data": {
                        "nickname": nickname,
                        "is_blocked": True,
                        "detect_result": check_result.get("detect_result"),
                        "label_code": check_result.get("label_code"),
                        "rewrite_content": check_result.get("rewrite_content"),
                        "violation_reason": self._get_violation_reason(check_result.get("label_code"))
                    },
                    "timestamp": datetime.now().isoformat()
                }
                
                self.logger.bind(tag=TAG).warning(f"[昵称上传接口] 昵称违规被拦截: {nickname}")
                response = web.json_response(response_data, status=200)
                return self._add_cors_headers(response)
            
            # 昵称检测通过，只返回结果不保存到Redis
            self.logger.bind(tag=TAG).info(f"[昵称上传接口] 昵称检测通过，不保存到Redis - client_id: {client_id}")

            response_data = {
                "success": True,
                "message": "昵称上传成功",
                "data": {
                    "nickname": nickname,
                    "is_blocked": False,
                    "detect_result": check_result.get("detect_result", "PASS")
                },
                "timestamp": datetime.now().isoformat()
            }

            # 记录最终响应数据
            self.logger.bind(tag=TAG).info(f"[昵称上传接口] 返回给客户端的响应: {json.dumps(response_data, ensure_ascii=False)}")

            response = web.json_response(response_data)
            return self._add_cors_headers(response)

        except json.JSONDecodeError:
            response = web.json_response({
                "success": False,
                "message": "JSON格式错误"
            })
            return self._add_cors_headers(response)
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"上传昵称时发生错误: {e}")
            response = web.json_response({
                "success": False,
                "message": f"服务器内部错误: {str(e)}"
            })
            return self._add_cors_headers(response)

    def _get_violation_reason(self, label_code: str) -> str:
        """根据违规标签获取用户友好的违规原因说明"""
        if not label_code:
            return "内容不符合社区规范"
        
        reason_map = {
            "Illegal": "包含违法犯罪内容",
            "Porn": "包含色情内容", 
            "Politics": "包含涉政内容",
            "Terrorism": "包含暴恐内容",
            "Star": "包含不当明星内容"
        }
        
        return reason_map.get(label_code, f"内容违规 ({label_code})")

    async def _handle_audio_summary(self, request: web.Request) -> Response:
        """上传会议录音分片，用于生成会议纪要"""
        try:
            # 验证认证信息
            if not await self._authenticate_request(request):
                return self._create_auth_error_response("认证失败，请检查client-id和authorization头信息")
            
            # 检查音频处理器是否可用
            if not self.audio_processor:
                response = web.json_response({
                    "success": False,
                    "message": "音频处理服务不可用"
                })
                return self._add_cors_headers(response)
            
            client_id = request.headers.get('client-id', '')
            
            # 检查请求是否包含文件
            if not request.content_type or not request.content_type.startswith('multipart/form-data'):
                response = web.json_response({
                    "success": False,
                    "message": "请求必须是multipart/form-data格式"
                })
                return self._add_cors_headers(response)
            
            # 读取上传的文件和JSON
            reader = await request.multipart()
            audio_file = None
            filename = None
            task_data = None
            
            async for field in reader:
                if field.name == 'audio' and field.filename:
                    filename = field.filename
                    audio_file = await field.read()
                elif field.name == 'json':
                    json_content = (await field.read()).decode('utf-8')
                    try:
                        task_data = json.loads(json_content)
                    except json.JSONDecodeError:
                        response = web.json_response({
                            "success": False,
                            "message": "客户端请升级到最新版，请求格式应该是JSON"
                        })
                        return self._add_cors_headers(response)
            
            # 检查是否上传了JSON
            if task_data is None:
                response = web.json_response({
                    "success": False,
                    "message": "客户端请升级到最新版"
                })
                return self._add_cors_headers(response)
            
            if not audio_file or not filename:
                response = web.json_response({
                    "success": False,
                    "message": "未找到音频文件，请确保字段名为'audio'"
                })
                return self._add_cors_headers(response)
            
            # 验证文件类型
            if not filename.lower().endswith('.mp3'):
                response = web.json_response({
                    "success": False,
                    "message": "只支持MP3格式的音频文件"
                })
                return self._add_cors_headers(response)
            
            # 验证文件大小 (限制为200MB)
            max_size = 200 * 1024 * 1024  # 200MB
            if len(audio_file) > max_size:
                response = web.json_response({
                    "success": False,
                    "message": f"文件大小超过限制，最大允许200MB，当前文件大小: {len(audio_file)/1024/1024:.2f}MB"
                })
                return self._add_cors_headers(response)
            
            # 获取或生成task_id
            task_id = task_data.get('task_id')
            if not task_id:
                # 生成新的task_id
                task_id = str(uuid.uuid4())
                self.logger.bind(tag=TAG).info(f"[音频摘要接口] 生成新的task_id: {task_id}")
            else:
                self.logger.bind(tag=TAG).info(f"[音频摘要接口] 使用已有task_id: {task_id}")
            
            self.logger.bind(tag=TAG).info(f"[音频摘要接口] 收到文件: {filename}, 大小: {len(audio_file)} bytes, 客户端: {client_id}, 任务: {task_id}")
            
            # 启动异步处理任务
            await self.audio_processor.start_audio_summary_task(
                audio_bytes=audio_file,
                filename=filename,
                meeting_task_id=task_id
            )
            
            response_data = {
                "success": True,
                "message": "音频摘要任务已启动",
                "data": {
                    "task_id": task_id,
                    "status": "processing",
                    "filename": filename,
                    "file_size": len(audio_file)
                },
                "timestamp": datetime.now().isoformat()
            }

            # 记录最终响应数据
            self.logger.bind(tag=TAG).info(f"[音频摘要接口] 返回给客户端的响应: {json.dumps(response_data, ensure_ascii=False)}")

            response = web.json_response(response_data)
            return self._add_cors_headers(response)

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"处理音频摘要请求时发生错误: {e}")
            response = web.json_response({
                "success": False,
                "message": f"服务器内部错误: {str(e)}"
            })
            return self._add_cors_headers(response)
    
    async def _handle_get_audio_summary(self, request: web.Request) -> Response:
        """查询音频摘要任务状态和结果"""
        try:
            # 验证认证信息
            if not await self._authenticate_request(request):
                return self._create_auth_error_response("认证失败，请检查client-id和authorization头信息")
            
            # 检查音频处理器是否可用
            if not self.audio_processor:
                response = web.json_response({
                    "success": False,
                    "message": "音频处理服务不可用"
                })
                return self._add_cors_headers(response)
            
            # 获取会议任务ID
            meeting_task_id = request.match_info.get('task_id')
            if not meeting_task_id:
                response = web.json_response({
                    "success": False,
                    "message": "缺少任务ID"
                })
                return self._add_cors_headers(response)
            
            client_id = request.headers.get('client-id', '')
            self.logger.bind(tag=TAG).info(f"[查询音频摘要] 查询会议任务: {meeting_task_id}, 客户端: {client_id}")
            
            # 查询会议任务状态
            meeting_info = await self.audio_processor.get_meeting_status(meeting_task_id)
            
            if not meeting_info:
                response = web.json_response({
                    "success": False,
                    "message": "任务不存在或已过期"
                })
                return self._add_cors_headers(response)
            
            # 构建响应数据
            response_data = {
                "success": True,
                "message": "任务状态查询成功",
                "data": {
                    "task_id": meeting_task_id,
                    "status": meeting_info["status"],
                    "last_updated": meeting_info["last_updated"]
                },
                "timestamp": datetime.now().isoformat()
            }
            
            # 根据任务状态添加额外信息
            if meeting_info["status"] == "completed":
                response_data["data"]["summary"] = meeting_info["result"]
                response_data["data"]["summary_length"] = len(meeting_info["result"]) if meeting_info["result"] else 0
                response_data["data"]["version"] = meeting_info.get("version", 1)
            elif meeting_info["status"] == "failed":
                response_data["data"]["error"] = meeting_info["error"]
            
            # 记录最终响应数据
            self.logger.bind(tag=TAG).info(f"[查询音频摘要] 会议任务{meeting_task_id}状态: {meeting_info['status']}")

            response = web.json_response(response_data)
            return self._add_cors_headers(response)

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"查询音频摘要任务时发生错误: {e}")
            response = web.json_response({
                "success": False,
                "message": f"服务器内部错误: {str(e)}"
            })
            return self._add_cors_headers(response)