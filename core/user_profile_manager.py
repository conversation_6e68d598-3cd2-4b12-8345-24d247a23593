import time
import datetime
from typing import Dict, Any, Optional, List
from config.logger import setup_logging

class UserProfileManager:
    """
    Manages user-related and chat-related functionality including:
    - User profile management
    - Role configuration
    - Chat scenarios and greetings
    - User settings persistence
    """
    
    def __init__(self, connection):
        """
        Initialize the UserProfileManager with a connection to the parent ConnectionHandler.
        
        Args:
            connection: Reference to the parent ConnectionHandler instance
        """
        self.connection = connection
        self.logger = setup_logging()
        
        # Initialize voice mapper
        from core.utils.voice_mapper import get_voice_mapper
        self.voice_mapper = get_voice_mapper()
        
        # Role and assistant settings
        self.current_role = None
        self.assistant_name = "语嫣"  # Default assistant name
        self.user_name = None  # User's name/nickname
        
        # Voice configuration - centralized TTS management
        self.current_voice_name = None
        self.current_voice_id = None
        self.current_voice_engine = None
        
        # Proactive chat settings
        self.last_proactive_time = {}  # Track last proactive message time per user
        self.daily_greetings_sent = {}  # Track daily greetings sent per user per scenario
        self.proactive_cooldown = 3600 * 3  # 3 hour cooldown between proactive messages
        self.pending_proactive_reply = {}  # Track users who received proactive messages but haven't replied
        
        
    async def load_user_profile(self):
        """Load user's role, assistant name, voice settings, and other profile information"""
        try:
            # Load user settings from Redis or memory system
            user_data = None
            if self.connection.redis_client and self.connection.redis_client.enabled and self.connection.client_id:
                try:
                    user_data = self.connection.redis_client.get_user_data(self.connection.client_id)
                    if user_data:
                        self.logger.info(f"[用户配置] 从Redis加载用户数据成功: client_id={self.connection.client_id}, 数据={user_data}")
                    else:
                        self.logger.info(f"[用户配置] Redis中未找到用户数据: client_id={self.connection.client_id}")
                except Exception as e:
                    self.logger.error(f"[用户配置] 从Redis加载用户数据失败: {e}")
            else:
                self.logger.warning("[用户配置] Redis客户端不可用，使用默认设置")
            # Set role from user data or use default
            if user_data and "role" in user_data:
                self.current_role = user_data["role"]
                self.logger.info(f"[用户配置] 从用户数据加载角色: {self.current_role}")
            else:
                self.current_role = self.connection.role_loader.get_default_role()
                self.logger.info(f"[用户配置] 使用默认角色: {self.current_role}")
                
            # Set assistant name from user data if available
            if user_data and "assistant_name" in user_data:
                self.assistant_name = user_data["assistant_name"]
                self.logger.info(f"Loaded assistant name: {self.assistant_name}")
                
            # Set user name from user data if available
            if user_data and "user_name" in user_data:
                self.user_name = user_data["user_name"]
                self.logger.info(f"Loaded user name: {self.user_name}")
            
            # Load voice configuration
            if user_data:
                self.current_voice_name = user_data.get("current_voice_name")
                self.current_voice_id = user_data.get("current_voice_id")
                self.current_voice_engine = user_data.get("current_voice_engine")
                
                if self.current_voice_name:
                    self.logger.info(f"Loaded voice config: {self.current_voice_name} ({self.current_voice_id}, {self.current_voice_engine})")
                
            # Apply role configuration based on user data
            await self.apply_role_config(user_data)
            
            # TTS voice will be set by apply_role_config based on the loaded role
            # No need to separately load voice here as it's tied to the role
                    
        except Exception as e:
            self.logger.error(f"Failed to load user profile: {e}")
            # Fall back to default settings
            self.current_role = self.connection.role_loader.get_default_role()
            await self.apply_role_config()
    
    async def apply_role_config(self, user_data: Optional[Dict[str, Any]] = None):
        """
        Apply role configuration and handle different chat scenarios (first meeting, etc.)
        
        Args:
            user_data: Optional user data dictionary containing profile information
        """
        try:
            role_config = self.connection.role_loader.get_role_config(self.current_role)
            settings_changed = False
            
            if not role_config:
                self.logger.warning(f"Role config not found: {self.current_role} 如果没有找到，那么用 data/roles.yaml 里配置的默认角色")
                # 使用默认角色作为兜底
                default_role = self.connection.role_loader.get_default_role()
                self.current_role = default_role
                role_config = self.connection.role_loader.get_role_config(default_role)
                if not role_config:
                    self.logger.error(f"Default role config also not found: {default_role}")
                    return
                self.logger.info(f"Using default role as fallback: {default_role}")
                settings_changed = True  # 角色发生了变化，需要保存
                
            # Replace assistant name placeholder
            prompt = role_config["prompt"].replace("{{assistant_name}}", self.assistant_name)
            
            # Time guidance will be added later in the returning user section if needed
            
            # Scenario detection: Check if this is the first meeting
            is_first_meeting = await self._check_first_meeting()
            
            # Check proactive conditions for all scenarios
            should_proactive = False
            if is_first_meeting:
                # First meetings should always be proactive and enthusiastic
                should_proactive = True
                scenario = self.get_current_time_scenario()
                if scenario:
                    # Mark as pending proactive reply for first meeting
                    self.pending_proactive_reply[self.connection.client_id] = {
                        'time': time.time(),
                        'scenario': f"first_meeting_{scenario}"
                    }
                    self.logger.info(f"First meeting proactive greeting for {scenario}, waiting for user reply to start cooldown")
            else:
                should_proactive = self.should_send_proactive_message()
                if should_proactive:
                    # Mark that we've sent a proactive message but haven't started cooldown yet
                    scenario = self.get_current_time_scenario()
                    if scenario:
                        today = datetime.datetime.now().strftime("%Y-%m-%d")
                        client_daily_key = f"{self.connection.client_id}_{today}"
                        
                        if client_daily_key not in self.daily_greetings_sent:
                            self.daily_greetings_sent[client_daily_key] = set()
                        self.daily_greetings_sent[client_daily_key].add(scenario)
                        
                        # Mark as pending proactive reply instead of starting cooldown immediately
                        self.pending_proactive_reply[self.connection.client_id] = {
                            'time': time.time(),
                            'scenario': scenario
                        }
                        
                        self.logger.info(f"Proactive greeting sent for {scenario}, waiting for user reply to start cooldown")
            
            # Time-aware guidance is now handled by fixed greeting system in receiveAudioHandle.py
            
            # Add returning user context only for non-first meetings
            if not is_first_meeting and user_data and user_data.get("last_access_time"):
                # Generate greeting context for returning user
                greeting_context = self._generate_returning_greeting(user_data["last_access_time"])
                prompt += f"\n{greeting_context}"
                self.logger.info(f"Added returning user greeting context: {greeting_context[:100]}...")
            
            # Update connection config with the new prompt
            self.connection.config["prompt"] = prompt
            self.logger.info(f"Applied role config: {self.current_role}, Assistant: {self.assistant_name}")
            
            # 设置角色的声音配置
            # 如果用户没有保存的声音配置，使用角色的默认声音
            if not self.current_voice_name:
                default_voice = self.voice_mapper.get_default_voice_for_role(self.current_role)
                if default_voice:
                    self.set_voice_config(
                        default_voice["name"],
                        default_voice["voice_id"],
                        default_voice["engine"]
                    )
                    self.logger.info(f"[声音配置] 使用角色默认声音: {default_voice['name']}")
                else:
                    # 兜底：使用role_config中的配置
                    voice = role_config.get("voice")
                    voice_engine = role_config.get("voice_engine", "DoubaoTTS")
                    if voice:
                        self.set_voice_config("未知声音", voice, voice_engine)
                        self.logger.info(f"[声音配置] 使用兜底声音配置: {voice}")
            else:
                # 用户已有声音配置，同步到连接的TTS配置中
                self.set_voice_config(self.current_voice_name, self.current_voice_id, self.current_voice_engine)
                self.logger.info(f"[声音配置] 恢复用户声音配置: {self.current_voice_name}")
                
            # Only save user settings if there were changes
            if settings_changed:
                self.save_user_settings()
                self.logger.debug("User settings saved due to configuration changes")
                
        except Exception as e:
            self.logger.error(f"Failed to apply role config: {e}")
    
    async def _check_first_meeting(self) -> bool:
        """Check if this is the user's first meeting with the assistant"""
        if not (self.connection.redis_client and self.connection.redis_client.enabled and self.connection.client_id):
            return True
            
        try:
            # Check if memory exists
            memory_exists = self.connection.redis_client.get_memory(self.connection.client_id) is not None
            if not memory_exists:
                self.logger.info(f"First meeting detected: client_id={self.connection.client_id}")
                return True
        except Exception as e:
            self.logger.warning(f"Failed to check first meeting status: {e}")
            
        return False
    
    def _generate_returning_greeting(self, last_access_time: float) -> str:
        """Generate an appropriate greeting based on time since last access
        
        Args:
            last_access_time: Timestamp of last access
            
        Returns:
            str: Generated greeting message
        """
        current_time = time.time()
        time_diff = current_time - float(last_access_time)
        
        # Generate base greeting based on time difference
        if time_diff < 60:  # Less than 1 minute
            greeting = "1分钟内你和用户刚刚聊过。"
        elif time_diff < 3600:  # Less than 1 hour
            minutes = int(time_diff / 60)
            greeting = f"{minutes}分钟前你刚和用户聊过。"
        elif time_diff < 86400:  # Less than 1 day
            hours = int(time_diff / 3600)
            greeting = f"你在{hours}小时前和用户聊过。"
        elif time_diff < 604800:  # Less than 1 week
            days = int(time_diff / 86400)
            greeting = f"你在{days}天前和用户聊过。"
        else:  # More than 1 week
            weeks = int(time_diff / 604800)
            greeting = f"你和用户有{weeks}周没聊过了。"
        
        # 用户姓名信息已经在 _get_user_context() 中包含，避免重复
            
        # Format timestamp to readable date and time with AM/PM
        dt = datetime.datetime.fromtimestamp(current_time)
        formatted_time = dt.strftime("%Y年%m月%d日%I:%M%p")
        return f"现在是{formatted_time}, " + greeting
    
    def save_user_settings(self):
        """Save user settings to Redis"""
        if not self.connection.client_id:
            return
            
        try:
            if self.connection.redis_client and self.connection.redis_client.enabled:
                user_data = self.connection.redis_client.get_user_data(self.connection.client_id) or {}
                user_data["role"] = self.current_role
                user_data["assistant_name"] = self.assistant_name
                if self.user_name:
                    user_data["user_name"] = self.user_name
                    
                    # 同时更新profile中的name字段保持一致性
                    if "profile" not in user_data:
                        user_data["profile"] = {}
                    user_data["profile"]["name"] = self.user_name
                
                # 保存声音配置
                if self.current_voice_name:
                    user_data["current_voice_name"] = self.current_voice_name
                if self.current_voice_id:
                    user_data["current_voice_id"] = self.current_voice_id
                if self.current_voice_engine:
                    user_data["current_voice_engine"] = self.current_voice_engine
                
                self.connection.redis_client.save_user_data(self.connection.client_id, user_data)
                
                # Get voice info for logging
                log_msg = f"User settings saved: Role={self.current_role}, Assistant={self.assistant_name}"
                if self.user_name:
                    log_msg += f", User={self.user_name}"
                if self.current_voice_name:
                    log_msg += f", Voice={self.current_voice_name}({self.current_voice_engine})"
                self.logger.info(log_msg)
        except Exception as e:
            self.logger.error(f"Failed to save user settings: {e}")
    
    def set_user_name(self, user_name: str) -> bool:
        """
        Set user name and save to Redis
        
        Args:
            user_name: User's name or nickname
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not user_name or not user_name.strip():
                self.logger.warning("User name is empty or invalid")
                return False
                
            old_name = self.user_name
            self.user_name = user_name.strip()
            
            # Save to Redis immediately
            self.save_user_settings()
            
            # Log the change
            if old_name:
                self.logger.info(f"User name updated: {old_name} -> {self.user_name}")
            else:
                self.logger.info(f"User name set: {self.user_name}")
                
            return True
        except Exception as e:
            self.logger.error(f"Failed to set user name: {e}")
            return False
    
    def get_user_name(self) -> Optional[str]:
        """
        Get current user name

        Returns:
            str: User name if set, None otherwise
        """
        return self.user_name

    def set_assistant_name(self, assistant_name: str) -> bool:
        """
        Set assistant name and save to Redis

        Args:
            assistant_name: Assistant's name

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not assistant_name or not assistant_name.strip():
                self.logger.warning("Assistant name is empty or invalid")
                return False

            old_name = self.assistant_name
            self.assistant_name = assistant_name.strip()

            # Save to Redis immediately
            self.save_user_settings()

            # Log the change
            if old_name != self.assistant_name:
                self.logger.info(f"Assistant name updated: {old_name} -> {self.assistant_name}")
            else:
                self.logger.info(f"Assistant name set: {self.assistant_name}")

            return True
        except Exception as e:
            self.logger.error(f"Failed to set assistant name: {e}")
            return False

    def get_assistant_name(self) -> str:
        """
        Get current assistant name

        Returns:
            str: Assistant name
        """
        return self.assistant_name

    def get_current_time_scenario(self) -> str:
        """Determine current time scenario (morning/noon/afternoon/evening)"""
        current_hour = datetime.datetime.now().hour
        current_minute = datetime.datetime.now().minute

        if 6 <= current_hour < 11:
            return "morning"
        elif 11 <= current_hour < 14:
            return "noon"
        elif 14 <= current_hour < 17 or (current_hour == 17 and current_minute < 30):
            return "afternoon"
        else:
            return "evening"  # 17:30-6:00 is evening


    def should_send_proactive_message(self) -> bool:
        """Check if we should send a proactive message based on cooldown, scenario, and daily limits"""
        if not self.connection.client_id:
            self.logger.info("无client_id，不发送主动消息")
            return False

        # Check if it's an appropriate time scenario
        scenario = self.get_current_time_scenario()
        if not scenario:
            current_hour = datetime.datetime.now().hour
            self.logger.info(f"当前时间({current_hour}点)不适合发送主动消息")
            return False

        # Check if we've already sent a greeting for this scenario today
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        client_daily_key = f"{self.connection.client_id}_{today}"

        if client_daily_key not in self.daily_greetings_sent:
            self.daily_greetings_sent[client_daily_key] = set()

        if scenario in self.daily_greetings_sent[client_daily_key]:
            self.logger.info(f"今日已发送过{scenario}问候，跳过")
            return False  # Already sent greeting for this time period today

        # Check cooldown (load from Redis if not in memory)
        current_time = time.time()
        last_time = self._get_last_proactive_time()
        time_diff = current_time - last_time

        if time_diff < self.proactive_cooldown:
            remaining = self.proactive_cooldown - time_diff
            self.logger.info(f"主动消息冷却中，还需{remaining:.0f}秒")
            return False

        # Check if user has been inactive (important: don't interrupt active conversations)
        try:
            if self.connection.redis_client and self.connection.redis_client.enabled:
                user_data = self.connection.redis_client.get_user_data(self.connection.client_id)
                if user_data and user_data.get("last_access_time"):
                    # Only send proactive message if user has been away for at least 30 minutes
                    time_since_last_access = current_time - float(user_data["last_access_time"])
                    # 检查是否是新连接（连接时间少于5秒）
                    connection_duration = current_time - getattr(self.connection, 'connection_start_time', current_time)
                    is_new_connection = connection_duration < 5

                    # 新连接检测：只有当上次访问时间超过冷却期时才允许新连接打招呼
                    if is_new_connection:
                        if time_diff >= self.proactive_cooldown:
                            self.logger.info(f"新连接检测到，且已过冷却期，允许主动打招呼")
                        else:
                            remaining = self.proactive_cooldown - time_diff
                            self.logger.info(f"新连接检测到，但冷却期未过，还需{remaining:.0f}秒")
                            return False
                    elif time_since_last_access < 1800:  # 30 minutes
                        self.logger.info(f"用户最近活动时间太近({time_since_last_access:.0f}秒前)，不发送主动消息")
                        return False
                else:
                    self.logger.info("用户数据中无last_access_time，允许发送主动消息")
            else:
                self.logger.info("Redis不可用，允许发送主动消息")
        except Exception as e:
            self.logger.warning(f"Failed to check last access time: {e}")

        self.logger.info(f"所有条件满足，可以发送{scenario}主动消息")
        return True

    def _get_last_proactive_time(self) -> float:
        """Get last proactive time from memory or Redis"""
        if not self.connection.client_id:
            return 0

        # Check memory first
        if self.connection.client_id in self.last_proactive_time:
            return self.last_proactive_time[self.connection.client_id]

        # Load from Redis if not in memory
        try:
            if self.connection.redis_client and self.connection.redis_client.enabled:
                user_data = self.connection.redis_client.get_user_data(self.connection.client_id)
                if user_data:
                    # Load last proactive time
                    if "last_proactive_time" in user_data:
                        last_time = float(user_data["last_proactive_time"])
                        self.last_proactive_time[self.connection.client_id] = last_time
                        return last_time
        except Exception as e:
            self.logger.warning(f"Failed to load last proactive time from Redis: {e}")

        return 0

    def handle_user_reply(self):
        """Handle user reply to proactive message - start cooldown timer"""
        if not self.connection.client_id:
            return

        # Check if there's a pending proactive reply
        if self.connection.client_id in self.pending_proactive_reply:
            pending_info = self.pending_proactive_reply[self.connection.client_id]

            # For first meetings, we need special handling to distinguish between:
            # 1. User's initial message that triggers the bot's proactive greeting
            # 2. User's reply to the bot's proactive greeting
            if pending_info['scenario'].startswith('first_meeting_'):
                # Use an instance variable to track if bot has sent proactive greeting in this session
                if not hasattr(self, '_bot_has_sent_first_greeting'):
                    # This is the user's first message that triggers bot's proactive greeting
                    self._bot_has_sent_first_greeting = True
                    self.logger.info(f"User's first message triggered bot's proactive greeting, not starting cooldown yet")
                    return
                else:
                    # This is a subsequent message - user is replying to bot's proactive greeting
                    self.pending_proactive_reply.pop(self.connection.client_id)
                    # Now start the cooldown timer
                    self.last_proactive_time[self.connection.client_id] = time.time()
                    self._save_proactive_time_to_redis()
                    self.logger.info(f"User replied to proactive {pending_info['scenario']} greeting, cooldown started")
            else:
                # For non-first meetings, start cooldown immediately
                self.pending_proactive_reply.pop(self.connection.client_id)
                # Now start the cooldown timer
                self.last_proactive_time[self.connection.client_id] = time.time()
                self._save_proactive_time_to_redis()
                self.logger.info(f"User replied to proactive {pending_info['scenario']} greeting, cooldown started")

    def _save_proactive_time_to_redis(self):
        """Save last proactive time to Redis"""
        if not self.connection.client_id:
            return

        try:
            if self.connection.redis_client and self.connection.redis_client.enabled:
                user_data = self.connection.redis_client.get_user_data(self.connection.client_id) or {}
                user_data["last_proactive_time"] = self.last_proactive_time.get(self.connection.client_id, 0)
                self.connection.redis_client.save_user_data(self.connection.client_id, user_data)
                self.logger.debug(f"Saved last proactive time to Redis: {user_data['last_proactive_time']}")
        except Exception as e:
            self.logger.error(f"Failed to save proactive time to Redis: {e}")


    def _cleanup_old_daily_records(self):
        """Clean up old daily greeting records to prevent memory leaks"""
        try:
            current_date = datetime.datetime.now().strftime("%Y-%m-%d")
            keys_to_remove = []

            for key in self.daily_greetings_sent.keys():
                # Extract date from key (format: client_id_YYYY-MM-DD)
                if "_" in key:
                    key_date = key.split("_")[-1]
                    if key_date != current_date:
                        keys_to_remove.append(key)

            for key in keys_to_remove:
                del self.daily_greetings_sent[key]

            if keys_to_remove:
                self.logger.info(f"Cleaned up {len(keys_to_remove)} old daily greeting records")

        except Exception as e:
            self.logger.warning(f"Failed to cleanup old daily records: {e}")

    def _get_user_context(self) -> str:
        """获取用户档案和偏好信息的上下文字符串
        
        Returns:
            str: 格式化的用户档案和偏好信息，如果没有信息则返回空字符串
        """
        user_profile_context = ""
        preferences_context = ""
        
        try:
            if (self.connection.redis_client and self.connection.redis_client.enabled and 
                self.connection.client_id):
                user_data = self.connection.redis_client.get_user_data(self.connection.client_id)
                if user_data:
                    # 提取用户档案信息（兼容新旧格式）
                    profile = user_data.get("profile", {})
                    preferences = user_data.get("preferences", {})
                    
                    # 处理用户档案信息，同时支持新旧字段
                    if profile or user_data.get("user_gender") or user_data.get("user_birth"):
                        profile_parts = []
                        
                        # 优先使用profile中的数据，再fallback到旧字段
                        name = profile.get("name") or user_data.get("user_name", "")
                        if name: profile_parts.append(f"姓名: {name}")
                        
                        if profile.get("age"): profile_parts.append(f"年龄: {profile['age']}")
                        
                        # 性别：优先使用profile.gender，再使用user_gender
                        gender = profile.get("gender") or user_data.get("user_gender", "")
                        if gender: profile_parts.append(f"性别: {gender}")
                        
                        if profile.get("occupation"): profile_parts.append(f"职业: {profile['occupation']}")
                        if profile.get("location"): profile_parts.append(f"地点: {profile['location']}")
                        
                        # 生活方式：优先使用profile.lifestyle，再使用lifestyle
                        lifestyle = profile.get("lifestyle") or user_data.get("lifestyle", "")
                        if lifestyle: profile_parts.append(f"生活方式: {lifestyle}")
                        
                        # 兴趣爱好：优先使用profile.interests，再使用interests
                        interests = profile.get("interests") or user_data.get("interests", "")
                        if interests:
                            if isinstance(interests, list):
                                interests_str = ", ".join(interests)
                            else:
                                interests_str = str(interests)
                            profile_parts.append(f"兴趣爱好: {interests_str}")
                        
                        # 生日：优先使用profile.important_dates，再使用user_birth
                        birth_date = profile.get("important_dates") or user_data.get("user_birth", "")
                        if birth_date: profile_parts.append(f"生日: {birth_date}")
                        
                        if profile_parts:
                            user_profile_context = f"用户档案: {'; '.join(profile_parts)}"
                    
                    if preferences:
                        pref_parts = []
                        if preferences.get("communication_style"): pref_parts.append(f"沟通风格: {preferences['communication_style']}")
                        if preferences.get("special_requests"): pref_parts.append(f"特殊要求: {preferences['special_requests']}")
                        
                        if pref_parts:
                            preferences_context = f"用户偏好: {'; '.join(pref_parts)}"
                    
                    if user_profile_context or preferences_context:
                        self.logger.info(f"[用户上下文] 获取到用户档案和偏好信息")
        except Exception as e:
            self.logger.warning(f"[用户上下文] 获取用户档案信息失败: {e}")
        
        # 构建带括号的用户信息块
        if user_profile_context or preferences_context:
            context_parts = []
            if user_profile_context:
                context_parts.append(user_profile_context)
            if preferences_context:
                context_parts.append(preferences_context)
            return f"（已知用户信息：{'; '.join(context_parts)}）\n"
        else:
            return ""


    def get_voice(self) -> str:
        """Get the current voice ID."""
        return self.current_voice_id or ""
    
    def get_voice_engine(self) -> str:
        """Get the current voice engine."""
        return self.current_voice_engine or "DoubaoTTS"
    
    def get_voice_name(self) -> str:
        """Get the current voice name."""
        return self.current_voice_name or ""
    
    def get_current_voice_config(self) -> Dict[str, Any]:
        """Get complete current voice configuration."""
        return {
            "voice": self.current_voice_id,
            "voice_engine": self.current_voice_engine,
            "voice_name": self.current_voice_name
        }
    
    def set_voice_config(self, voice_name: str, voice_id: str, voice_engine: str) -> bool:
        """
        Set voice configuration
        
        Args:
            voice_name: 声音名称
            voice_id: TTS引擎的voice_id
            voice_engine: TTS引擎名称
            
        Returns:
            bool: 设置是否成功
        """
        try:
            self.current_voice_name = voice_name
            self.current_voice_id = voice_id
            self.current_voice_engine = voice_engine
            
            # Voice configuration is now centrally managed by UserProfileManager
            # No need to sync to connection's _user_tts_config
            
            self.logger.info(f"声音配置已更新: {voice_name} ({voice_id}, {voice_engine})")
            return True
        except Exception as e:
            self.logger.error(f"设置声音配置失败: {e}")
            return False
    
    def get_available_voices_for_role(self, role_name: str = None) -> List[Dict]:
        """
        获取指定角色的可用声音列表
        
        Args:
            role_name: 角色名称，如果为None则使用当前角色
            
        Returns:
            声音配置列表
        """
        target_role = role_name or self.current_role
        if not target_role:
            return []
        
        return self.voice_mapper.get_role_voices(target_role)
    
    def switch_to_next_voice(self) -> bool:
        """
        切换到下一个声音（循环切换）
        
        Returns:
            bool: 切换是否成功
        """
        if not self.current_role:
            self.logger.warning("无当前角色，无法切换声音")
            return False
        
        next_voice = self.voice_mapper.get_next_voice(self.current_role, self.current_voice_name)
        if not next_voice:
            self.logger.warning(f"未找到角色 {self.current_role} 的下一个声音")
            return False
        
        success = self.set_voice_config(
            next_voice["name"],
            next_voice["voice_id"], 
            next_voice["engine"]
        )
        
        if success:
            self.save_user_settings()
            self.logger.info(f"声音已切换到: {next_voice['name']}")
        
        return success
    
    def switch_to_voice_by_name(self, voice_name: str) -> bool:
        """
        切换到指定名称的声音
        
        Args:
            voice_name: 声音名称
            
        Returns:
            bool: 切换是否成功
        """
        if not self.current_role:
            self.logger.warning("无当前角色，无法切换声音")
            return False
        
        voice_config = self.voice_mapper.get_voice_by_name(self.current_role, voice_name)
        if not voice_config:
            self.logger.warning(f"未找到角色 {self.current_role} 的声音: {voice_name}")
            return False
        
        success = self.set_voice_config(
            voice_config["name"],
            voice_config["voice_id"],
            voice_config["engine"]
        )
        
        if success:
            self.save_user_settings()
            self.logger.info(f"声音已切换到: {voice_name}")
        
        return success
