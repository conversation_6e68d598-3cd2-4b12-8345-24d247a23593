import json
import asyncio
import time
from core.utils.util import get_string_no_punctuation_or_emoji, analyze_emotion
from core.utils.connection_cleanup import clear_speak_status

TAG = __name__

emoji_map = {
    "neutral": "😶",
    "happy": "🙂",
    "laughing": "😆",
    "funny": "😂",
    "sad": "😔",
    "angry": "😠",
    "crying": "😭",
    "loving": "😍",
    "embarrassed": "😳",
    "surprised": "😲",
    "shocked": "😱",
    "thinking": "🤔",
    "winking": "😉",
    "cool": "😎",
    "relaxed": "😌",
    "delicious": "🤤",
    "kissy": "😘",
    "confident": "😏",
    "sleepy": "😴",
    "silly": "😜",
    "confused": "🙄",
}


async def sendAudioMessage(conn, audios, text, text_index=0):
    # 检查内容是否被拦截，如果被拦截则跳过发送
    if getattr(conn, 'content_blocked', False):
        conn.logger.bind(tag=TAG).debug(f"🚫 TTS消息跳过 - 内容已被拦截: {text_index}")
        return
    
    # 记录开始时间用于性能分析
    audio_start_time = time.perf_counter()
    
    # 发送句子开始消息
    if text is not None:
        emotion = analyze_emotion(text)
        emoji = emoji_map.get(emotion, "🙂")  # 默认使用笑脸
        conn.logger.bind(tag=TAG).info(f"发送情感消息JSON: {emoji}")
        await conn.websocket.send(
            json.dumps(
                {
                    "type": "llm",
                    "text": emoji,
                    "emotion": emotion,
                    "session_id": conn.session_id,
                }
            )
        )

    if text_index == conn.tts_first_text_index:
        conn.logger.bind(tag=TAG).info(f"发送第一段语音: {text}")
    await send_tts_message(conn, "sentence_start", text)

    # 优化音频播放策略
    is_first_audio = text_index == conn.tts_first_text_index
    
    # 检查是否有TTSManager统计信息来优化播放
    adaptive_timing = True
    if hasattr(conn, 'tts_manager') and conn.tts_manager:
        stats = conn.tts_manager.get_client_stats(conn.client_id)
        if stats:
            # 如果有预加载缓存，可以更激进地播放
            if stats.get('preload_cache_count', 0) > 0:
                adaptive_timing = True
                conn.logger.bind(tag=TAG).debug(
                    f"检测到预加载缓存 {stats['preload_cache_count']} 项，启用优化播放"
                )
    
    await sendAudio(conn, audios, pre_buffer=is_first_audio, adaptive_timing=adaptive_timing)
    
    audio_duration = (time.perf_counter() - audio_start_time) * 1000
    conn.logger.bind(tag=TAG).debug(
        f"句子 {text_index} 音频播放完成，耗时: {audio_duration:.2f}ms"
    )

    await send_tts_message(conn, "sentence_end", text)

    # 发送结束消息（如果是最后一个文本）
    if conn.llm_finish_task and text_index == conn.tts_last_text_index:
        await send_tts_message(conn, "stop", None)
        
        # 🚨 调试：记录关键状态信息
        conn.logger.bind(tag=TAG).warning(f"🎵 TTS播放完全结束 - close_after_chat={getattr(conn, 'close_after_chat', False)}")
        conn.logger.bind(tag=TAG).warning(f"🎵 - max_output_size={getattr(conn, 'max_output_size', 0)}")
        conn.logger.bind(tag=TAG).warning(f"🎵 - session_id={conn.session_id}")
        
        if getattr(conn, 'close_after_chat', False):
            conn.logger.bind(tag=TAG).error(f"🚨 检测到close_after_chat=True，即将自动关闭连接！")
            
            # 记录调用栈以便调试
            import traceback
            conn.logger.bind(tag=TAG).error(f"🚨 自动关闭调用栈：\n{traceback.format_stack()}")
            
            await conn.close()


# 播放音频
async def sendAudio(conn, audios, pre_buffer=True, adaptive_timing=True):
    # 流控参数优化
    frame_duration = 60  # 帧时长（毫秒），匹配 Opus 编码
    start_time = time.perf_counter()
    play_position = 0
    last_reset_time = time.perf_counter()  # 记录最后的重置时间
    
    # 自适应预缓冲策略
    if pre_buffer:
        # 根据网络延迟动态调整预缓冲帧数
        base_pre_buffer = 3
        # 如果检测到高延迟，增加预缓冲
        network_delay = getattr(conn, '_last_network_delay', 0)
        if network_delay > 100:  # 100ms以上延迟
            base_pre_buffer = min(6, len(audios))  # 增加预缓冲
        elif network_delay < 50:  # 低延迟网络
            base_pre_buffer = min(2, len(audios))  # 减少预缓冲
        
        pre_buffer_frames = min(base_pre_buffer, len(audios))
        conn.logger.bind(tag=TAG).debug(
            f"自适应预缓冲: {pre_buffer_frames}帧 (网络延迟: {network_delay}ms)"
        )
        
        # 快速发送预缓冲帧
        for i in range(pre_buffer_frames):
            conn.logger.bind(tag=TAG).info(f"发送预缓冲音频帧: {i}")
            await conn.websocket.send(audios[i])
        remaining_audios = audios[pre_buffer_frames:]
    else:
        remaining_audios = audios

    # 播放剩余音频帧
    frame_count = 0
    batch_size = 3  # 批量发送帧数
    
    for i, opus_packet in enumerate(remaining_audios):
        if conn.client_abort:
            return

        # 每分钟重置一次计时器
        if time.perf_counter() - last_reset_time > 60:
            await conn.reset_timeout()
            last_reset_time = time.perf_counter()

        # 自适应时序控制
        if adaptive_timing:
            # 计算预期发送时间
            expected_time = start_time + (play_position / 1000)
            current_time = time.perf_counter()
            delay = expected_time - current_time
            
            # 动态调整延迟，避免过度等待
            if delay > 0:
                # 限制最大延迟，避免播放卡顿
                max_delay = 0.1  # 最大延迟100ms
                actual_delay = min(delay, max_delay)
                if actual_delay > 0.001:  # 只在延迟大于1ms时sleep
                    await asyncio.sleep(actual_delay)
            elif delay < -0.05:  # 如果落后超过50ms，记录网络延迟
                conn._last_network_delay = abs(delay * 1000)
        
        # 记录发送时间以供分析
        send_start = time.perf_counter()
        await conn.websocket.send(opus_packet)
        send_duration = (time.perf_counter() - send_start) * 1000
        
        # 记录异常慢的发送
        if send_duration > 10:  # 发送超过10ms
            conn.logger.bind(tag=TAG).warning(
                f"音频帧发送耗时过长: {send_duration:.2f}ms (帧{i})"
            )
        
        play_position += frame_duration
        frame_count += 1
        
        # 批量处理时稍作让步，避免阻塞事件循环
        if frame_count % batch_size == 0:
            await asyncio.sleep(0.001)  # 1ms让步


async def send_tts_message(conn, state, text=None):
    """发送 TTS 状态消息"""
    message = {"type": "tts", "state": state, "session_id": conn.session_id}
    if text is not None:
        message["text"] = text

    # TTS播放结束
    if state == "stop":
        # 播放提示音
        tts_notify = conn.config.get("enable_stop_tts_notify", False)
        if tts_notify:
            stop_tts_notify_voice = conn.config.get(
                "stop_tts_notify_voice", "config/assets/tts_notify.mp3"
            )
            audios, _ = conn.tts.audio_to_opus_data(stop_tts_notify_voice)
            await sendAudio(conn, audios, pre_buffer=False, adaptive_timing=False)  # 提示音不需要预缓冲
        # 清除服务端讲话状态
        clear_speak_status(conn)

    # 发送消息到客户端
    conn.logger.bind(tag=TAG).info(f"发送 TTS 消息: {message}")
    await conn.websocket.send(json.dumps(message))
    
    # 记录状态变化时间，用于性能分析
    if hasattr(conn, '_tts_state_times'):
        conn._tts_state_times[state] = time.perf_counter()
    else:
        conn._tts_state_times = {state: time.perf_counter()}


async def send_stt_message(conn, text):
    """发送 STT 状态消息"""
    stt_text = get_string_no_punctuation_or_emoji(text)
    conn.logger.bind(tag=TAG).info(f"发送 STT 消息: {stt_text}")
    await conn.websocket.send(
        json.dumps({"type": "stt", "text": stt_text, "session_id": conn.session_id})
    )
    await send_tts_message(conn, "start")

