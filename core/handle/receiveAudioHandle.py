import time
import copy
import asyncio
from core.utils.util import remove_punctuation_and_length
from core.handle.sendAudioHandle import send_stt_message
from core.handle.intentHandler import handle_user_intent
from core.utils.output_counter import check_device_output_limit
from core.handle.reportHandle import enqueue_asr_report
from core.utils.util import audio_to_data
from core.utils.connection_cleanup import handle_barge_in, reset_vad_states, reset_chat_states
from core.providers.vad.stream_vad.doubao_vad import DoubaoStreamVAD

TAG = __name__


class StreamVADManager:
    """流式VAD管理器，处理基于ASR文本流的语音检测"""
    
    def __init__(self, conn):
        self.conn = conn
        self.logger = conn.logger
        
        # 初始化Doubao流式VAD
        try:
            self.doubao_vad = DoubaoStreamVAD(conn.config)
            self.logger.bind(tag=TAG).info(f"🎯 StreamVADManager初始化成功")
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"🎯 StreamVADManager初始化失败: {e}")
            self.doubao_vad = None
        
        # 存储最新的ASR结果
        self.latest_asr_result = None
        self.latest_asr_response = None
        self.speech_completed = False
        
        # 线程锁确保线程安全
        import threading
        self._lock = threading.Lock()
    
    def on_asr_result(self, client_id: str, text: str, asr_response: dict):
        """接收来自流式ASR的结果回调"""
        if client_id != self.conn.client_id:
            return
        
        with self._lock:
            self.latest_asr_result = text
            self.latest_asr_response = asr_response
            
            # 使用DoubaoStreamVAD检测语音是否完成
            if self.doubao_vad:
                self.speech_completed = self.doubao_vad.is_speech_complete(asr_response)
                
                if self.speech_completed:
                    self.logger.bind(tag=TAG).info(f"🎯 检测到语音完成: {text}")
                else:
                    self.logger.bind(tag=TAG).debug(f"🎯 语音进行中: {text}")
    
    def check_speech_status(self) -> bool:
        """检查语音状态，替代传统VAD的is_vad方法"""
        with self._lock:
            if self.speech_completed:
                return True
            
            # 如果没有流式ASR结果，使用传统VAD作为备选
            if self.latest_asr_result is None:
                return False
            
            return False
    
    def get_completed_text(self) -> tuple:
        """获取完成的语音文本，替代传统ASR的speech_to_text方法"""
        with self._lock:
            if self.speech_completed and self.latest_asr_result:
                text = self.latest_asr_result
                self.logger.bind(tag=TAG).info(f"🎯 返回完成的语音文本: {text}")
                
                # 重置状态
                self.reset_state()
                
                return text, None
            else:
                return "", None
    
    def reset_state(self):
        """重置VAD状态"""
        with self._lock:
            self.latest_asr_result = None
            self.latest_asr_response = None
            self.speech_completed = False
            
            if self.doubao_vad:
                self.doubao_vad.reset()
            
            self.logger.bind(tag=TAG).debug(f"🎯 StreamVADManager状态已重置")
    
    def is_available(self) -> bool:
        """检查StreamVADManager是否可用"""
        return self.doubao_vad is not None


async def handleAudioMessage(conn, audio):
    # 初始化StreamVADManager
    stream_vad = get_stream_vad_manager(conn)
    
    # 如果StreamVAD不可用，回退到传统VAD
    if not stream_vad.is_available():
        if conn.vad is None:
            return
        # 使用传统VAD逻辑
        await _handle_traditional_vad(conn, audio)
        return
    
    # 【流式VAD逻辑】并行处理流式ASR - 音频数据到达就立即处理
    try:
        from core.providers.asr.doubao_v3 import process_streaming_asr
        # 创建异步任务进行流式ASR处理，不阻塞主流程
        asyncio.create_task(process_streaming_asr(conn, audio))
    except Exception as e:
        conn.logger.bind(tag=TAG).debug(f"🎤 流式ASR处理异常: {e}")

    # 使用流式VAD检测语音状态
    if conn.client_listen_mode == "auto" or conn.client_listen_mode == "realtime":
        # 检查流式VAD是否检测到语音完成
        speech_completed = stream_vad.check_speech_status()
        
        # 如果语音完成，处理识别结果
        if speech_completed:
            conn.logger.bind(tag=TAG).info(f"🎯 流式VAD检测到语音完成")
            
            # 设置语音停止状态
            conn.client_voice_stop = True
            conn.client_have_voice = True
            
            # 处理语音结束逻辑
            await _handle_speech_completed(conn, stream_vad)
        else:
            # 语音进行中，标记有语音
            if stream_vad.latest_asr_result:
                conn.client_have_voice = True
                conn.client_voice_stop = False
            else:
                # 没有ASR结果，表示没有语音
                conn.client_have_voice = False
                conn.client_voice_stop = False
    else:
        # 手动模式，使用客户端控制
        if conn.client_have_voice and stream_vad.check_speech_status():
            await _handle_speech_completed(conn, stream_vad)


async def _handle_traditional_vad(conn, audio):
    """处理传统VAD逻辑（保留作为备选）"""
    # 【新增】并行处理流式ASR - 音频数据到达就立即处理
    try:
        from core.providers.asr.doubao_v3 import process_streaming_asr
        # 创建异步任务进行流式ASR处理，不阻塞主流程
        asyncio.create_task(process_streaming_asr(conn, audio))
    except Exception as e:
        conn.logger.bind(tag=TAG).debug(f"🎤 流式ASR处理异常: {e}")

    if conn.client_listen_mode == "auto" or conn.client_listen_mode == "realtime":
        have_voice = conn.vad.is_vad(conn, audio)
    else:
        have_voice = conn.client_have_voice

    # 如果本次没有声音，本段也没声音，就把声音丢弃了
    if have_voice == False and conn.client_have_voice == False:
        conn.asr_audio.append(audio)
        conn.asr_audio = conn.asr_audio[
            -10:
        ]  # 保留最新的10帧音频内容，解决ASR句首丢字问题
        return
    conn.asr_audio.append(audio)
    # 如果本段有声音，且已经停止了
    if conn.client_voice_stop:
        conn.client_abort = False
        conn.asr_server_receive = False
        # 音频太短了，无法识别
        if len(conn.asr_audio) < 15:
            conn.asr_server_receive = True
        else:
            # 先打断还没说完的LLM和TTS任务
            await handle_barge_in(conn)
            
            # 然后开始语音识别（保留但不调用）
            # text, _ = await conn.asr.speech_to_text(conn.asr_audio, conn.session_id)
            
            # 使用空文本作为占位符，实际不进行聊天
            text = ""
            text_len, _ = remove_punctuation_and_length(text)
            if text_len > 0:
                # 使用自定义模块进行上报
                enqueue_asr_report(conn, text, copy.deepcopy(conn.asr_audio))

                await startToChat(conn, text)
            else:
                conn.asr_server_receive = True
        conn.asr_audio.clear()
        reset_vad_states(conn)


async def _handle_speech_completed(conn, stream_vad):
    """处理语音完成逻辑"""
    conn.client_abort = False
    conn.asr_server_receive = False
    
    # 先打断还没说完的LLM和TTS任务
    await handle_barge_in(conn)
    
    # 从流式VAD获取完成的语音文本
    text, _ = stream_vad.get_completed_text()
    text_len, _ = remove_punctuation_and_length(text)
    
    if text_len > 0:
        # 使用自定义模块进行上报（使用空音频数组作为占位符）
        enqueue_asr_report(conn, text, [])
        
        await startToChat(conn, text)
    else:
        conn.asr_server_receive = True
    
    # 清理状态
    conn.asr_audio.clear()
    reset_vad_states(conn)
    stream_vad.reset_state()


async def startToChat(conn, text):
    # 会议记录模式：只进行ASR识别，跳过后续所有处理
    if conn.meeting_mode:
        await send_meeting_message(conn, text)
        conn.asr_server_receive = True
        conn.logger.bind(tag=TAG).info(f"会议记录模式 - ASR结果: {text}")
        return

    if conn.need_bind:
        await check_bind_device(conn)
        return

    # 如果当日的输出字数大于限定的字数
    if conn.max_output_size > 0:
        if check_device_output_limit(
            conn.headers.get("device-id"), conn.max_output_size
        ):
            await max_out_size(conn)
            return

    # 检查是否是初次连接的固定问候语（如"Hi~"）
    if await handle_initial_greeting(conn, text):
        # 如果是初次连接问候语，已处理完成
        # 注意：不立即恢复收音，让TTS播放完成后自动恢复
        return

    # 首先进行意图分析
    intent_handled = await handle_user_intent(conn, text)

    if intent_handled:
        # 如果意图已被处理，不再进行聊天
        # 注意：不立即恢复收音，让TTS播放完成后自动恢复
        return

    # 意图未被处理，继续常规聊天流程
    # 【重要修复】重置所有相关状态，确保新的聊天请求不会被之前的中断影响
    reset_chat_states(conn)

    await send_stt_message(conn, text)
    
    # 提交聊天任务到线程池，并添加异常处理
    try:
        # 使用统一的聊天方法，支持function calling和常规聊天
        future = conn.executor.submit(conn.chat_with_function_calling, text)
        
        # 为future添加完成回调，用于捕获线程池中的异常
        def handle_chat_exception(fut):
            try:
                fut.result()  # 获取结果，如果有异常会抛出
            except Exception as e:
                conn.logger.bind(tag=TAG).error(f"🚨 聊天处理线程异常: {e}")
                import traceback
                conn.logger.bind(tag=TAG).debug(f"🚨 聊天线程异常堆栈: {traceback.format_exc()}")
        
        future.add_done_callback(handle_chat_exception)
        
    except Exception as e:
        conn.logger.bind(tag=TAG).error(f"🚨 提交聊天任务到线程池失败: {e}")
        # 设置ASR接收状态，让系统可以处理下一个请求
        conn.asr_server_receive = True




async def max_out_size(conn):
    text = "不好意思，我现在有点事情要忙，明天这个时候我们再聊，约好了哦！明天不见不散，拜拜！"
    await send_stt_message(conn, text)
    conn.tts_first_text_index = 0
    conn.tts_last_text_index = 0
    conn.llm_finish_task = True
    file_path = "config/assets/max_output_size.wav"
    opus_packets, _ = audio_to_data(file_path)
    conn.audio_play_queue.put((opus_packets, text, 0))
    conn.close_after_chat = True


async def check_bind_device(conn):
    if conn.bind_code:
        # 确保bind_code是6位数字
        if len(conn.bind_code) != 6:
            conn.logger.bind(tag=TAG).error(f"无效的绑定码格式: {conn.bind_code}")
            text = "绑定码格式错误，请检查配置。"
            await send_stt_message(conn, text)
            return

        text = f"请登录控制面板，输入{conn.bind_code}，绑定设备。"
        await send_stt_message(conn, text)
        conn.tts_first_text_index = 0
        conn.tts_last_text_index = 6
        conn.llm_finish_task = True

        # 播放提示音
        music_path = "config/assets/bind_code.wav"
        opus_packets, _ = audio_to_data(music_path)
        conn.audio_play_queue.put((opus_packets, text, 0))

        # 逐个播放数字
        for i in range(6):  # 确保只播放6位数字
            try:
                digit = conn.bind_code[i]
                num_path = f"config/assets/bind_code/{digit}.wav"
                num_packets, _ = audio_to_data(num_path)
                conn.audio_play_queue.put((num_packets, None, i + 1))
            except Exception as e:
                conn.logger.bind(tag=TAG).error(f"播放数字音频失败: {e}")
                continue
    else:
        text = f"没有找到该设备的版本信息，请正确配置 OTA地址，然后重新编译固件。"
        await send_stt_message(conn, text)
        conn.tts_first_text_index = 0
        conn.tts_last_text_index = 0
        conn.llm_finish_task = True
        music_path = "config/assets/bind_not_found.wav"
        opus_packets, _ = audio_to_data(music_path)
        conn.audio_play_queue.put((opus_packets, text, 0))


async def handle_initial_greeting(conn, text):
    """
    处理初次连接的固定问候语（如"Hi~"）
    根据时间段返回固定的问候文字，不调用LLM
    
    Args:
        conn: 连接对象
        text: 用户输入的文本
        
    Returns:
        bool: 如果是初次连接问候语并已处理，返回True；否则返回False
    """
    # 直接匹配客户端固定发送的"Hi"
    if text != "Hi":
        return False
    
    # 只要是"Hi~"就认为是session首句，直接处理
    
    conn.logger.bind(tag=TAG).info(f"📢 检测到初次连接问候语: '{text}'，生成固定时间段回复")
    
    # 获取用户配置管理器
    user_manager = conn.user_profile_manager
    
    # 获取当前时间段
    scenario = user_manager.get_current_time_scenario()
    
    # 获取用户昵称和助手名字
    user_name = user_manager.get_user_name()
    assistant_name = user_manager.get_assistant_name()
    
    # 构建固定的问候语
    greeting_text = _generate_fixed_greeting(scenario, user_name, assistant_name)
    
    conn.logger.bind(tag=TAG).info(f"📢 生成固定问候语: '{greeting_text}'")
    
    # 发送STT消息（显示用户说的话）
    await send_stt_message(conn, text)
    
    # 设置TTS相关状态
    conn.tts_first_text_index = 0
    conn.tts_last_text_index = 0
    conn.llm_finish_task = True
    
    # 生成TTS音频
    try:
        import asyncio
        import os
        
        # 使用TTSManager处理TTS任务以避免并发冲突
        if conn.tts_manager and conn.client_id:
            # 从用户配置获取voice_engine和voice
            voice_engine = conn.user_profile_manager.get_voice_engine()
            voice = conn.user_profile_manager.get_voice()
            
            conn.logger.bind(tag=TAG).debug(f"📢 使用TTSManager处理固定问候语TTS: engine={voice_engine}, voice={voice}")
            
            # 提交TTS任务到管理器
            future = conn.tts_manager.submit_task(conn.client_id, greeting_text, 0, voice_engine, voice)
            if future is None:
                conn.logger.bind(tag=TAG).warning(f"📢 TTSManager提交固定问候语任务失败: client_id={conn.client_id}")
            else:
                try:
                    # 等待TTS任务完成 - TTSManager会自动处理音频队列
                    tts_timeout = int(conn.config.get("tts_timeout", 10))
                    tts_file, text_result, text_index_result = await asyncio.to_thread(future.result, timeout=tts_timeout)
                    
                    # 【修复】使用TTSManager时，tts_file为None是正常的，因为TTSManager内部处理所有音频
                    if tts_file and os.path.exists(tts_file):
                        # 传统TTS方式成功
                        conn.logger.bind(tag=TAG).info(f"📢 固定问候语TTS音频已通过TTSManager加入播放队列")
                    elif tts_file is None and text_result:
                        # TTSManager方式成功 - tts_file为None是正常的
                        conn.logger.bind(tag=TAG).info(f"📢 固定问候语TTS已通过TTSManager成功处理")
                    else:
                        conn.logger.bind(tag=TAG).error(f"📢 固定问候语TTS生成失败")
                        
                except Exception as e:
                    conn.logger.bind(tag=TAG).error(f"📢 固定问候语TTSManager任务失败: {e}")
        else:
            # 回退到原有的直接调用方式
            conn.logger.bind(tag=TAG).warning("📢 TTSManager不可用，回退到直接TTS调用")
            tts_file = await asyncio.to_thread(conn.tts.to_tts, greeting_text)
            
            if tts_file and os.path.exists(tts_file):
                opus_packets, _ = audio_to_data(tts_file)
                conn.audio_play_queue.put((opus_packets, greeting_text, 0))
                conn.logger.bind(tag=TAG).info(f"📢 固定问候语TTS音频已加入播放队列")
            else:
                conn.logger.bind(tag=TAG).error(f"📢 固定问候语TTS生成失败")
            
    except Exception as e:
        conn.logger.bind(tag=TAG).error(f"📢 固定问候语TTS处理异常: {e}")
    
    # 处理用户回复逻辑（更新冷却时间等）
    user_manager.handle_user_reply()
    
    # 异步预加载历史记录+policy检查
    if conn.memory is not None:
        conn.logger.bind(tag=TAG).info(f"📢 开始异步预加载历史记录")
        asyncio.create_task(_preload_memory_with_policy_check(conn))
    
    return True


async def _preload_memory_with_policy_check(conn):
    """
    异步预加载历史记录并进行policy检查
    在用户说"Hi"时并行执行，利用TTS合成时间
    """
    try:
        conn.logger.bind(tag=TAG).info(f"📢 开始从Redis获取历史记录")
        
        # 从Redis获取历史记录
        memory_summary = await conn.memory.query_memory("")
        
        if memory_summary:
            conn.logger.bind(tag=TAG).info(f"📢 获取到历史记录，长度: {len(memory_summary)}字符")
            
            # 对历史记录进行policy检查
            if conn.policy_check:
                conn.logger.bind(tag=TAG).info(f"📢 开始对历史记录进行policy检查")
                check_result = await conn.policy_check.check_answer("总结聊天记录", memory_summary)
                
                if check_result.get("is_blocked", False):
                    conn.logger.bind(tag=TAG).warning(f"📢 历史记录被policy检查拦截: {check_result.get('detect_result')}")
                    conn.cached_memory_summary = ""
                    conn.memory_policy_checked = False
                else:
                    conn.logger.bind(tag=TAG).info(f"📢 历史记录通过policy检查")
                    conn.cached_memory_summary = memory_summary
                    conn.memory_policy_checked = True
            else:
                # 如果没有policy检查，直接使用
                conn.cached_memory_summary = memory_summary
                conn.memory_policy_checked = True
        else:
            conn.logger.bind(tag=TAG).info(f"📢 没有获取到历史记录")
            conn.cached_memory_summary = ""
            conn.memory_policy_checked = True
            
    except Exception as e:
        conn.logger.bind(tag=TAG).error(f"📢 预加载历史记录失败: {e}")
        conn.cached_memory_summary = ""
        conn.memory_policy_checked = False


def _generate_fixed_greeting(scenario, user_name, assistant_name):
    """
    根据时间段生成固定的问候语
    
    Args:
        scenario: 时间段 (morning/noon/afternoon/evening)
        user_name: 用户昵称
        assistant_name: 助手名字
        
    Returns:
        str: 生成的固定问候语
    """
    # 时间段对应的问候语
    time_greetings = {
        "morning": "早上好",
        "noon": "中午好", 
        "afternoon": "下午好",
        "evening": "晚上好"
    }
    
    # 获取时间段问候语，默认为"你好"
    time_greeting = time_greetings.get(scenario, "你好")
    
    # 构建用户称呼部分
    user_part = ""
    if user_name and user_name.strip():
        user_part = f"，{user_name}"
    
    # 构建完整问候语
    greeting = f"{time_greeting}{user_part}。我是你的搭子{assistant_name}，今天有什么要跟我分享的吗？"
    
    return greeting


def get_stream_vad_manager(conn):
    """获取或创建StreamVADManager实例"""
    if not hasattr(conn, 'stream_vad_manager') or conn.stream_vad_manager is None:
        conn.stream_vad_manager = StreamVADManager(conn)
    return conn.stream_vad_manager
