# YuYan服务微服务化Docker Compose配置

services:
  # WebSocket服务 - 处理实时语音对话
  websocket-server:
    profiles: ["gpu"]
    build:
      context: .
      dockerfile: Dockerfile.websocket
    container_name: yuyan-websocket
    ports:
      - "8000:8000"   # WebSocket端口
      - "8080:8080"   # OTA端口
    volumes:
      - ${SECRETS_DIR:-./secrets}:/app/secrets
      - ${MODELS_DIR:-./models}:/app/models
      - ${TMP_DIR:-./tmp}:/app/tmp
    environment:
      - SERVICE_NAME=websocket
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_DB=${REDIS_DB:-0}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - REDIS_ENABLED=${REDIS_ENABLED:-true}
      - TZ=Asia/Shanghai
      - CUDA_VISIBLE_DEVICES=${CUDA_VISIBLE_DEVICES:-all}
      - NVIDIA_VISIBLE_DEVICES=${NVIDIA_VISIBLE_DEVICES:-all}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import socket; s=socket.socket(); s.connect(('localhost', 8000)); s.close()"]
      interval: 60s
      timeout: 15s
      retries: 5
      start_period: 120s
    networks:
      - yuyan-network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # WebSocket服务 - CPU模式（大内存）
  websocket-server-cpu:
    build:
      context: .
      dockerfile: Dockerfile.websocket
    container_name: yuyan-websocket-cpu
    ports:
      - "8000:8000"   # WebSocket端口
      - "8080:8080"   # OTA端口
    volumes:
      - ${SECRETS_DIR:-./secrets}:/app/secrets
      - ${MODELS_DIR:-./models}:/app/models
      - ${TMP_DIR:-./tmp}:/app/tmp
    environment:
      - SERVICE_NAME=websocket
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_DB=${REDIS_DB:-0}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - REDIS_ENABLED=${REDIS_ENABLED:-true}
      - TZ=Asia/Shanghai
      - CUDA_VISIBLE_DEVICES=""
      - NVIDIA_VISIBLE_DEVICES=""
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import socket; s=socket.socket(); s.connect(('localhost', 8000)); s.close()"]
      interval: 60s
      timeout: 15s
      retries: 5
      start_period: 120s
    networks:
      - yuyan-network
    deploy:
      resources:
        limits:
          memory: 10G
          cpus: '2.0'
        reservations:
          memory: 8G
          cpus: '1.0'

  # HTTP API服务 - 处理RESTful API请求
  http-api-server:
    build:
      context: .
      dockerfile: Dockerfile.http
    container_name: yuyan-http-api
    ports:
      - "8100:8100"   # HTTP API端口
    volumes:
      - ${SECRETS_DIR:-./secrets}:/app/secrets
      - ${TMP_DIR:-./tmp}:/app/tmp
    environment:
      - SERVICE_NAME=http-api
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_DB=${REDIS_DB:-0}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - REDIS_ENABLED=${REDIS_ENABLED:-true}
      - TZ=Asia/Shanghai
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8100/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - yuyan-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'



networks:
  yuyan-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16