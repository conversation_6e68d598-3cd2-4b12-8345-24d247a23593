关键日志条目解释：

1、从日志里可以看到客户端和服务端通信的过程：
2025-06-27 23:17:44 - 0.4.3_SiFiGeDoshca - core.handle.textHandle - INFO - core.handle.textHandle - 收到文本消息：{"type":"hello","transport":"websocket","audio_params":{"format":"opus","sample_rate":16000,"channels":1,"frame_duration":60},"version":1}
2025-06-27 23:19:25 - 0.4.3_SiFiGeDoshca - core.handle.textHandle - INFO - core.handle.textHandle - 收到文本消息：{"type":"listen","state":"start","mode":"auto","session_id":"c79852a5-e3b6-4a95-bfee-7b24deda41dc","version":1}
...
（其它类型的消息略）

session_id 就是会话ID.

2、WebSocket协议内建的心跳：
2025-06-27 23:17:44 - 0.4.3_SiFiGeDoshca - websockets.protocol - DEBUG - websockets.protocol - = connection is OPEN
2025-06-27 23:18:14 - 0.4.3_SiFiGeDoshca - websockets.protocol - DEBUG - websockets.protocol - < PING '' [0 bytes]
2025-06-27 23:18:14 - 0.4.3_SiFiGeDoshca - websockets.protocol - DEBUG - websockets.protocol - > PONG '' [0 bytes]
通过计算首个PING离建立连接（日志connection is OPEN）的间隔时间、以及每个PING/PONG的间隔时间、以及PONG和下一个PING之间的间隔时间，如果超过3秒，就有连接自动断开的风险

3、连接的客户端的IP和唯一ID（client-id）
2025-06-27 23:17:44 - 0.4.3_SiFiGeDoshca - core.connection - INFO - core.connection - *********** conn - Headers: {'host': 'chat-api-yuyan.zzfx.net.cn', 'x-real-ip': '***************', 'x-forwarded-for': '***************', 'upgrade': 'websocket', 'connection': 'Upgrade', 'user-agent': 'Dart/3.7 (dart:io)', 'protocol-version': '1', 'cache-control': 'no-cache', 'accept-encoding': 'gzip', 'authorization': 'Bearer pmc=qdRIhWwuzE7fsr2eCuOOilTpqa6RcuPJwsF2rmKuXk5taTy70jOy3WUlz6rvMFbifL8L+SztaqSIrf1C9rlPZMgyiu3+B5COdg6PuMfBuHTC7+IxZNauiSonFwGiSwHGgVJw1ykKRAFIvDfCA6iZEfG0jmGbBOmAy4mUUGIuVm0=;psf=kN9HjTg9XwrLiDx6yyQq0tkOymxy2riJQMTtvBEeAeQJf6nrR5NHze5S0YsQ6B1g5vpFPvggHgloOQ3WnkdYuQ%3D%3D;pms=BgQc%2B6Lt5M84RM4YJa6qnaUCXgTC%2FbknQcxYDjhIEBkIbfgebrphfjW5ngSm2fwod%2BJRPV6gB1GQGLBI6I7SaBoHL6hjR%2FLO7bTyY%2BOpHwHS8peuPUTpCUmMwgNEjX9I1z7PPG%2FzssseaLuhL91Z5h3VGqiwDsKsfcKw1EuxiTI%3D;psu=IbfMOyR8cEIqGAQjrxwEb2g3QR1lPd90nOsM9c7%2BHeDjXPlVvYVQ6GR7r7sWVUiu30qiFJc3ACCpCz05vKcahtmXzvanN8hJbljPiy2sQzoGdYp%2FqnNzSFe9bkRVlWvL7I7jK46vyUNNtRwuz7kXd8nJcyz7IDDJsPTRlM%2BlzVA%3D;', 'device-id': '02:c1:74:de:3a:a6', 'sec-websocket-key': 'tTWC7aFXWBqlauh1wrCY9A==', 'client-id': 'to7dt4bnfp127psmcewo3lxuzp9z43lx', 'sec-websocket-version': '13', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}

4、目前客户端连接上以后，是固定先发一个Hi~，然后靠服务端回复。日志里会看到这条：
2025-06-27 23:17:44 - 0.4.3_SiFiGeDoshca - core.handle.textHandle - INFO - core.handle.textHandle - 收到文本消息：{"type":"listen","state":"detect","mode":"auto","text":"Hi~","session_id":"c79852a5-e3b6-4a95-bfee-7b24deda41dc","version":1}

对于回复的内容，会看到一条日志：
2025-06-27 23:17:49 - 0.4.3_SiFiGeDoshca - core.providers.tts.base - INFO - core.providers.tts.base - 语音生成成功: 晚上好，小张。我是你的搭子小霖，今天有什么要跟我分享的吗？:tmp/tts-2025-06-27@3f0bda3176ac400281e44a641ab1d28a.mp3，重试0次

这表示TTS语音已经准备好，会发送给客户端，客户端会播放，从而让用户听到回复的声音。

下面这条日志显示了发送给客户端的耗时：
2025-06-27 23:17:55 - 0.4.3_SiFiGeDoshca - core.handle.sendAudioHandle - DEBUG - core.handle.sendAudioHandle - 句子 0 音频播放完成，耗时: 5704.90ms

当看到这条日志时，表示TTS播放已经完成：
2025-06-27 23:17:55 - 0.4.3_SiFiGeDoshca - websockets.protocol - DEBUG - websockets.protocol - > TEXT '{"type": "tts", "state": "stop", "session_id": ...a95-bfee-7b24deda41dc"}' [86 bytes]

接着看到这条日志：
2025-06-27 23:17:56 - 0.4.3_SiFiGeDoshca - core.handle.textHandle - INFO - core.handle.textHandle - 收到文本消息：{"type":"listen","state":"start","mode":"auto","session_id":"c79852a5-e3b6-4a95-bfee-7b24deda41dc","version":1}

表示客户端开始发送它收到的新的语音了。然后这条日志：
2025-06-27 23:18:00 - 0.4.3_SiFiGeDoshca - core.providers.asr.firered - DEBUG - core.providers.asr.firered - 语音识别耗时: 0.229s | 结果: 最近有什么科技新闻啊

表示语音识别完成。

下面的日志表明语音识别完的文本，已经开始发给LLM处理：
2025-06-27 23:18:00 - 0.4.3_SiFiGeDoshca - core.connection - DEBUG - core.connection - Chat with function calling start: 最近有什么科技新闻啊

在LLM实际处理之前，会先用policy_check审核：
2025-06-27 23:18:01 - 0.4.3_SiFiGeDoshca - core.policy_check - DEBUG - PolicyCheck - question内容检测通过 - 问题内容: 最近有什么科技新闻啊

下面的日志表明已经调用了LLM，等待返回：
2025-06-27 23:18:01 - 0.4.3_SiFiGeDoshca - core.providers.llm.gemini.gemini - DEBUG - core.providers.llm.gemini.gemini - 🌊 开始迭代stream chunks...

下面的日志是首字符响应，与上面这条日志联系在一起就可以计算首字符响应延迟：
2025-06-27 23:18:04 - 0.4.3_SiFiGeDoshca - core.connection - DEBUG - core.connection - 📦 收到第1个LLM响应

但是因为首个字符还不能拼成完整的句子，所以等后续stream trunk到了以后：
2025-06-27 23:18:05 - 0.4.3_SiFiGeDoshca - core.connection - INFO - core.connection - 大模型说出第一句话: 哎呀，你问我科技新闻？

这就可以计算首句延迟。

接着看到开始TTS生成：
2025-06-27 23:18:05 - 0.4.3_SiFiGeDoshca - core.utils.tts_manager - DEBUG - core.utils.tts_manager - [to7dt4bnfp127psmcewo3lxuzp9z43lx] 开始处理TTS任务 sentence_1 (优先级1): 哎呀，你问我科技新闻？...

到这个日志：
2025-06-27 23:18:09 - 0.4.3_SiFiGeDoshca - core.providers.tts.base - INFO - core.providers.tts.base - 语音生成成功: 哎呀，你问我科技新闻？:tmp/tts-2025-06-27@df705d5631504e93ae53ad6237e75d2d.mp3，重试0次

TTS合成完毕。

下面的日志是TTS播放完成。
2025-06-27 23:18:11 - 0.4.3_SiFiGeDoshca - core.handle.sendAudioHandle - INFO - core.handle.sendAudioHandle - 发送 TTS 消息: {'type': 'tts', 'state': 'sentence_end', 'session_id': 'c79852a5-e3b6-4a95-bfee-7b24deda41dc', 'text': '哎呀，你问我科技新闻？'}

后续句子以此类推。但注意TTS合成是并行的，时间线上可能有重叠。把这个重叠也标志出来，统计重叠节约的时间。


当看到这个日志的时候：
2025-06-28 08:47:48 - 0.4.3_SiFiGeDoshca - core.connection - WARNING - core.connection - 🔌 客户端断开连接 - 原因: CLIENT_NORMAL_CLOSE, 发起方: CLIENT, 会话ID: d70cf03e-711d-4553-b5a8-34d496f32b89, 客户端: 28r7v6fuks11001mc04ak631lbqm0307, 设备ID: 7f:83:ab:82:93:f9, 连接时长: 32.54秒

说明客户端连接断开了，里面有本次会话的时长。