本项目会启动一个 WebSocket 服务监听，手机客户端连接上以后，会发送语音或文本消息，服务端实时用语音和文本回复。

---

## 1. 总体流程概览

1. **客户端建立 WebSocket 连接**  
   - 当客户端需要开始语音会话时（例如用户唤醒、手动按键触发等）：
     - 连接本服务的 WebSocket URL
     - 设置若干请求头（`Authorization`, `Protocol-Version`, `Device-Id`, `Client-Id`）  

2. **客户端发送 "hello" 消息**  
   - 连接成功后，客户端会发送一条 JSON 消息，示例结构如下：  
   ```json
   {
     "type": "hello",
     "version": 1,
     "features": {
       "mcp": true
     },
     "transport": "websocket",
     "audio_params": {
       "format": "opus",
       "sample_rate": 16000,
       "channels": 1,
       "frame_duration": 60
     }
   }
   ```
   - 其中 `features` 字段为可选，内容根据客户端编译配置自动生成。例如：`"mcp": true` 表示支持 MCP 协议。
   - `frame_duration` 的值对应 `OPUS_FRAME_DURATION_MS`（例如 60ms）。

3. **服务器回复 "hello"**  
   - 客户端等待服务器返回一条包含 `"type": "hello"` 的 JSON 消息，并检查 `"transport": "websocket"` 是否匹配。  
   - 服务器可选下发 `session_id` 字段，客户端收到后会自动记录。  
   - 示例：
   ```json
   {
     "type": "hello",
     "transport": "websocket",
     "session_id": "xxx",
     "audio_params": {
       "format": "opus",
       "sample_rate": 24000,
       "channels": 1,
       "frame_duration": 60
     }
   }
   ```
   - 如果匹配，则认为服务器已就绪，标记音频通道打开成功。  
   - 如果在超时时间（默认 10 秒）内未收到正确回复，认为连接失败并触发网络错误回调。

4. **后续消息交互**  
   - 客户端和服务器端之间可发送两种主要类型的数据：  
     1. **二进制音频数据**（Opus 编码）  
     2. **文本 JSON 消息**（用于传输聊天状态、TTS/STT 事件、MCP 协议消息等）  

5. **客户端关闭 WebSocket 连接**  
   - 客户端在需要结束语音会话时，会主动断开连接。

---

## 2. 通用请求头

在建立 WebSocket 连接时，客户端设置了以下请求头：

- `Authorization`: 用于存放访问令牌，形如 `"Bearer <token>"`  
- `Protocol-Version`: 固定示例中为 `"1"`，与 hello 消息体内的 `version` 字段保持一致  
- `Device-Id`: 客户端物理网卡 MAC 地址
- `Client-Id`: 软件生成的 UUID

这些头会随着 WebSocket 握手一起发送到服务器，服务器可根据需求进行校验、认证等。

---

## 3. JSON 消息结构

WebSocket 文本帧以 JSON 方式传输，以下为常见的 `"type"` 字段及其对应业务逻辑。若消息里包含未列出的字段，可能为可选或特定实现细节。

### 3.1 客户端→服务器

1. **Hello**  
   - 连接成功后，由客户端发送，告知服务器基本参数。  
   - 例：
     ```json
     {
       "type": "hello",
       "version": 1,
       "features": {
         "mcp": true
       },
       "transport": "websocket",
       "audio_params": {
         "format": "opus",
         "sample_rate": 16000,
         "channels": 1,
         "frame_duration": 60
       }
     }
     ```

2. **Listen**  
   - 表示客户端开始或停止录音监听。  
   - 常见字段：  
     - `"session_id"`：会话标识  
     - `"type": "listen"`  
     - `"state"`：`"start"`, `"stop"`, `"detect"`（唤醒检测已触发）  
     - `"mode"`：`"auto"`, `"manual"` 或 `"realtime"`，表示识别模式。  
   - 例：开始监听  
     ```json
     {
       "session_id": "xxx",
       "type": "listen",
       "state": "start",
       "mode": "manual"
     }
     ```

3. **Abort**  
   - 终止当前说话（TTS 播放）或语音通道。  
   - 例：
     ```json
     {
       "session_id": "xxx",
       "type": "abort",
       "reason": "wake_word_detected"
     }
     ```
   - `reason` 值可为 `"wake_word_detected"` 或其他。

4. **Wake Word Detected**  
   - 用于客户端向服务器告知检测到唤醒词。
   - 在发送该消息之前，可提前发送唤醒词的 Opus 音频数据，用于服务器进行声纹检测。  
   - 例：
     ```json
     {
       "session_id": "xxx",
       "type": "listen",
       "state": "detect",
       "text": "你好小明"
     }
     ```

5. **MCP**
   - 推荐用于物联网控制的新一代协议。所有客户端能力发现、工具调用等均通过 type: "mcp" 的消息进行，payload 内部为标准 JSON-RPC 2.0（详见 [MCP 协议文档](./mcp-protocol.md)）。
   
   - **客户端到服务器发送 result 的例子：**
     ```json
     {
       "session_id": "xxx",
       "type": "mcp",
       "payload": {
         "jsonrpc": "2.0",
         "id": 1,
         "result": {
           "content": [
             { "type": "text", "text": "true" }
           ],
           "isError": false
         }
       }
     }
     ```

---

### 3.2 服务器→客户端

1. **Hello**  
   - 服务器端返回的握手确认消息。  
   - 必须包含 `"type": "hello"` 和 `"transport": "websocket"`。  
   - 可能会带有 `audio_params`，表示服务器期望的音频参数，或与客户端对齐的配置。   
   - 服务器可选下发 `session_id` 字段，客户端收到后会自动记录。  
   - 成功接收后客户端会设置事件标志，表示 WebSocket 通道就绪。

2. **STT**  
   - `{"session_id": "xxx", "type": "stt", "text": "..."}`
   - 表示服务器端识别到了用户语音。（例如语音转文本结果）  
   - 客户端可能将此文本显示到屏幕上，后续再进入回答等流程。

3. **LLM**  
   - `{"session_id": "xxx", "type": "llm", "emotion": "happy", "text": "😀"}`
   - 服务器指示客户端调整表情动画 / UI 表达。  

4. **TTS**  
   - `{"session_id": "xxx", "type": "tts", "state": "start"}`：服务器准备下发 TTS 音频，客户端进入 "speaking" 播放状态。  
   - `{"session_id": "xxx", "type": "tts", "state": "stop"}`：表示本次 TTS 结束。  
   - `{"session_id": "xxx", "type": "tts", "state": "sentence_start", "text": "..."}`：让客户端在界面上显示当前要播放或朗读的文本片段。
   - `{"session_id": "xxx", "type": "tts", "state": "sentence_end", "text": "..."}`：表示服务器已将一个句子的完整音频数据发送完毕。  

5. **MCP**
   - 服务器通过 type: "mcp" 的消息下发物联网相关的控制指令或返回调用结果，payload 结构同上。
   
   - **服务器到客户端发送 tools/call 的例子：**
     ```json
     {
       "session_id": "xxx",
       "type": "mcp",
       "payload": {
         "jsonrpc": "2.0",
         "method": "tools/call",
         "params": {
           "name": "self.light.set_rgb",
           "arguments": { "r": 255, "g": 0, "b": 0 }
         },
         "id": 1
       }
     }
     ```

6. **音频数据：二进制帧**  
   - 当服务器发送音频二进制帧（Opus 编码）时，客户端解码并播放。  
   - 若客户端正在处于 "listening" （录音）状态，收到的音频帧会被忽略或清空以防冲突。

---

## 4. 音频编解码

1. **客户端发送录音数据**  
   - 音频输入经过可能的回声消除、降噪或音量增益后，通过 Opus 编码打包为二进制帧发送给服务器。  
   - 如果客户端每次编码生成的二进制帧大小为 N 字节，则会通过 WebSocket 的 **binary** 消息发送这块数据。

2. **客户端播放收到的音频**  
   - 收到服务器的二进制帧时，同样认定是 Opus 数据。  
   - 客户端会进行解码，然后交由音频输出接口播放。  
   - 如果服务器的音频采样率与客户端不一致，会在解码后再进行重采样。

---

## 5. 语音交互时序详解

本章节结合日志，详细描述一次完整的“客户端提问 -> 服务端回答”的流程。

### 阶段一：客户端发送语音 & 服务端进行语音识别 (ASR)

1.  **客户端开始发送语音**
    -   用户触发语音输入后，客户端首先向服务端发送一条 `listen` 消息，告知服务端“我要开始说话了”。
    -   **客户端 -> 服务端 (JSON):**
        ```json
        {"type":"listen", "state":"start", "mode":"auto", "session_id":"..."}
        ```
    -   随后，客户端将采集到的用户语音数据，通过 Opus 编码后，以**二进制帧 (binary message)** 的形式，流式地发送给服务端。

2.  **服务端进行语音转文本 (ASR)**
    -   服务端接收客户端发来的二进制音频流。
    -   当服务端 VAD (Voice Activity Detection) 检测到用户停止说话后，会将收到的音频数据送入 ASR 引擎进行识别。
    -   **服务端日志 (ASR ���成):**
        ```log
        core.providers.asr.firered - DEBUG - 语音识别耗时: 0.229s | 结果: 最近有什么科技新闻啊
        ```
    -   识别完成后，服务端会向客户端发送一条 `stt` 消息，让客户端可以在界面上实时显示识别出的文本。
    -   **服务端 -> 客户端 (JSON):**
        ```json
        {"type": "stt", "text": "最近有什么科技新闻啊", "session_id":"..."}
        ```

### 阶段二：服务端处理文本 & 生成回复 (LLM + TTS)

1.  **大语言模型 (LLM) 处理**
    -   ASR 识别出的文本会立即被送往大语言模型 (LLM) 进行处理。
    -   **服务端日志 (LLM 开始处理):**
        ```log
        core.connection - DEBUG - Chat with function calling start: 最近有什么科技新闻啊
        ```
    -   LLM 以流式 (stream) 的方式返回文本结果。当第一个可用于合成语音的完整句子生成后，服务端立即开始下一步。
    -   **服务端日志 (LLM 返回第一句话):**
        ```log
        core.connection - INFO - 大模型说出第一句话: 哎呀，你问我科技新闻？
        ```

2.  **文本转语音 (TTS) 并行处理**
    -   为了达到最快的响应速度，服务端采用**边生成边播放**的策略。从 LLM 拿到第一句话的文本后，立即将其送入 TTS 引擎进行语音合成，**无需等待 LLM 完全响应结束**。
    -   **服务端日志 (TTS 开始合成):**
        ```log
        core.utils.tts_manager - DEBUG - 开始处理TTS任务 sentence_1 (优先级1): 哎呀，你问我科技新闻？...
        ```
    -   与此同时，LLM 会继续在后台生成第二、三...句话的文本。这些后续的文本也会被依次、并行地送入 TTS 引擎。

### 阶段三：服务端将文本和语音流式返回给客户端

这是实现实时对话感的关键。服务端会将生成的文本和语音交错、流式地发给客户端。

1.  **通知客户端准备播放**
    -   在发送第一帧音频数据前，服务端先发送 `tts` 的 `start` 状态，通知客户端：我要开始播放语音了，请做好准备（如显示“正在说话”的 UI 状态）。
    -   **服务端 -> 客户端 (JSON):**
        ```json
        {"type": "tts", "state": "start", "session_id": "..."}
        ```

2.  **发送句子文本**
    -   紧接着，服务端将当前正要播放的句子文本通过 `sentence_start` 消息发给客户端，用于在界面上展示。
    -   **服务端 -> 客户端 (JSON):**
        ```json
        {"type": "tts", "state": "sentence_start", "text": "哎呀，你问我科技新闻？", "session_id": "..."}
        ```

3.  **发送句子对应的音频流**
    -   TTS 引擎合成好的音频数据，通过 **二进制帧 (binary message)** 的形式，流式地发送给客户端。客户端收到后立即解码并播放。
    -   **服务端日志 (TTS 合成完毕):**
        ```log
        core.providers.tts.base - INFO - 语音生成成功: 哎呀，你问我科技新闻？:tmp/tts-....mp3
        ```

4.  **通知句子播放结束**
    -   当一句话的音频数据全部发送完毕后，服务端会发送一个 `sentence_end` 消息。
    -   **服务端日志 & 消息:**
        ```log
        core.handle.sendAudioHandle - INFO - 发送 TTS 消息: {'type': 'tts', 'state': 'sentence_end', ...}
        ```
    -   这个过程 (步骤 2-4) 会为 LLM 生成的每一句话重复执行，实现了文本和语音的同步流式播放。

5.  **通知全部播放结束**
    -   当所有句子都播放完毕后，服务端发送 `tts` 的 `stop` 状态，标志着本次回答结束。
    -   **服务端 -> 客户端 (JSON):**
        ```json
        {"type": "tts", "state": "stop", "session_id": "..."}
        ```
    -   此时，客户端可以切换回监听状态，等待用户的下一轮语音输入。