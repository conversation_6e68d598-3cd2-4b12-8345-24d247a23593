#!/bin/bash

# Docker 镜像构建统一脚本
# 用法: ./docker-build.sh [service] [tag]

set -e

SERVICE=${1:-all}
TAG=${2:-latest}

echo "🚀 开始构建 Docker 镜像..."

case $SERVICE in
    "websocket")
        echo "📡 构建 WebSocket 服务镜像..."
        docker build -f Dockerfile.websocket -t yuyan-websocket:$TAG .
        ;;
    "http")
        echo "🌐 构建 HTTP API 服务镜像..."
        docker build -f Dockerfile.http -t yuyan-http-api:$TAG .
        ;;
    "unified")
        echo "🔧 构建统一服务镜像..."
        docker build -t yuyan-server:$TAG .
        ;;
    "all")
        echo "🏗️  构建所有服务镜像..."
        docker build -f Dockerfile.websocket -t yuyan-websocket:$TAG .
        docker build -f Dockerfile.http -t yuyan-http-api:$TAG .
        docker build -t yuyan-server:$TAG .
        ;;
    *)
        echo "❌ 未知服务: $SERVICE"
        echo "可用选项: websocket, http, unified, all"
        exit 1
        ;;
esac

echo "✅ 构建完成!"
echo "📋 可用镜像:"
docker images | grep yuyan