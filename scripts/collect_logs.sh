#!/bin/bash

# 清理旧的日志文件
echo "Cleaning up old log files..."
rm -f server*.log

# 根据主机名设置容器数量
HOSTNAME=$(hostname)
if [[ $HOSTNAME == gpu001* ]]; then
  echo "Hostname '$HOSTNAME': collecting logs from 3 containers."
  CONTAINER_COUNT=3
else
  echo "Hostname '$HOSTNAME': collecting logs from 8 containers."
  CONTAINER_COUNT=8
fi

# 循环复制日志文件
LOG_FILES=""
for i in $(seq 1 $CONTAINER_COUNT); do
  CONTAINER_NAME="xiaozhi-esp32-server-$i"
  LOG_FILE="server$i.log"
  echo "Copying log from $CONTAINER_NAME to $LOG_FILE..."
  sudo docker cp "$CONTAINER_NAME:/opt/xiaozhi-esp32-server/tmp/server.log" "$LOG_FILE"
  if [ $? -eq 0 ]; then
    LOG_FILES="$LOG_FILES $LOG_FILE"
  else
    echo "Warning: Failed to copy log from $CONTAINER_NAME. It might not be running."
  fi
done

# 检查是否有日志文件被成功复制
if [ -z "$LOG_FILES" ]; then
  echo "No log files were collected. Aborting zip."
  exit 1
fi

# 压缩日志文件
ZIP_FILE="/tmp/log.zip"
echo "Zipping log files to $ZIP_FILE..."
rm -f "$ZIP_FILE"
zip "$ZIP_FILE" $LOG_FILES

# 清理本地的日志文件
echo "Cleaning up temporary log files..."
rm -f $LOG_FILES

echo "Log collection complete. The zip file is located at: $ZIP_FILE"
