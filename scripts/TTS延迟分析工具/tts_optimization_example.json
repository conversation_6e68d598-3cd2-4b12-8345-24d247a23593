{"tts": {"preload_enabled": true, "max_preload_count": 2, "preload_timeout": 10.0, "max_concurrent_tts": 4}, "optimization_notes": {"preload_enabled": "启用TTS预加载功能，在播放当前句子时并行生成下一句", "max_preload_count": "最大预加载句子数量，建议2-3句以平衡性能和资源消耗", "preload_timeout": "预加载任务超时时间(秒)，超时后自动取消", "max_concurrent_tts": "并发TTS生成的最大线程数，建议根据服务器性能调整"}, "performance_tips": ["1. 预加载功能需要预知下一句的文本内容，适合流式LLM输出", "2. 增加max_concurrent_tts可提高并发处理能力，但会消耗更多CPU和内存", "3. 调整max_preload_count平衡延迟和资源消耗：值过大会浪费资源，值过小效果不明显", "4. 监控日志中的'句子间延迟'来评估优化效果", "5. 使用analyze_tts_log.py脚本定期分析TTS性能"]}