#!/usr/bin/env python3
"""
TTS日志分析脚本
分析TTS.log中的时间间隔模式，统计sentence_end到sentence_start的延迟
"""

import re
import sys
from datetime import datetime
from typing import List, Dict, Tuple
import argparse


class TTSLogAnalyzer:
    def __init__(self, log_file_path: str):
        self.log_file_path = log_file_path
        self.events = []
        
    def parse_log(self):
        """解析日志文件"""
        print(f"正在解析日志文件: {self.log_file_path}")
        
        with open(self.log_file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    event = self._parse_log_line(line.strip(), line_num)
                    if event:
                        self.events.append(event)
                except Exception as e:
                    print(f"解析第{line_num}行时出错: {e}")
                    continue
        
        print(f"解析完成，共发现 {len(self.events)} 个相关事件")
        
    def _parse_log_line(self, line: str, line_num: int) -> Dict:
        """解析单行日志"""
        # 时间戳模式: 2025-06-27 11:59:08
        timestamp_pattern = r'^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})'
        
        # TTS相关事件模式
        patterns = {
            'tts_start': r'发送 TTS 消息.*"state": "start"',
            'tts_sentence_start': r'发送 TTS 消息.*"state": "sentence_start"',
            'tts_sentence_end': r'发送 TTS 消息.*"state": "sentence_end"',
            'tts_stop': r'发送 TTS 消息.*"state": "stop"',
            'tts_generation_start': r'开始Fish Audio TTS合成',
            'tts_generation_complete': r'Fish Audio TTS合成完成',
            'audio_send_start': r'发送第.*段语音',
            'audio_frame_send': r'发送预缓冲音频帧',
            'fish_request_start': r'🎯 开始TTS请求处理',
            'fish_request_complete': r'✅ TTS请求处理完成'
        }
        
        timestamp_match = re.search(timestamp_pattern, line)
        if not timestamp_match:
            return None
            
        timestamp_str = timestamp_match.group(1)
        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
        
        for event_type, pattern in patterns.items():
            if re.search(pattern, line):
                return {
                    'timestamp': timestamp,
                    'type': event_type,
                    'line': line,
                    'line_num': line_num
                }
        
        return None
    
    def analyze_delays(self):
        """分析延迟模式"""
        print("\n=== TTS延迟分析 ===")
        
        # 分析sentence_end到sentence_start的延迟
        sentence_delays = self._analyze_sentence_delays()
        if sentence_delays:
            self._print_delay_stats("句子间延迟", sentence_delays)
        
        # 分析TTS生成时间
        generation_times = self._analyze_generation_times()
        if generation_times:
            self._print_delay_stats("TTS生成时间", generation_times)
            
        # 分析Fish Audio API调用时间
        api_call_times = self._analyze_api_call_times()
        if api_call_times:
            self._print_delay_stats("Fish Audio API调用时间", api_call_times)
    
    def _analyze_sentence_delays(self) -> List[float]:
        """分析句子间延迟"""
        delays = []
        last_sentence_end = None
        
        for event in self.events:
            if event['type'] == 'tts_sentence_end':
                last_sentence_end = event['timestamp']
            elif event['type'] == 'tts_sentence_start' and last_sentence_end:
                delay = (event['timestamp'] - last_sentence_end).total_seconds()
                delays.append(delay)
                print(f"句子间延迟: {delay:.2f}秒 (行{event['line_num']})")
                last_sentence_end = None
        
        return delays
    
    def _analyze_generation_times(self) -> List[float]:
        """分析TTS生成时间"""
        times = []
        start_time = None
        
        for event in self.events:
            if event['type'] == 'tts_generation_start':
                start_time = event['timestamp']
            elif event['type'] == 'tts_generation_complete' and start_time:
                duration = (event['timestamp'] - start_time).total_seconds()
                times.append(duration)
                print(f"TTS生成时间: {duration:.2f}秒 (行{start_time} -> {event['line_num']})")
                start_time = None
        
        return times
    
    def _analyze_api_call_times(self) -> List[float]:
        """分析Fish Audio API调用时间"""
        times = []
        start_time = None
        
        for event in self.events:
            if event['type'] == 'fish_request_start':
                start_time = event['timestamp']
            elif event['type'] == 'fish_request_complete' and start_time:
                duration = (event['timestamp'] - start_time).total_seconds()
                times.append(duration)
                print(f"Fish Audio API调用时间: {duration:.2f}秒 (行{start_time} -> {event['line_num']})")
                start_time = None
        
        return times
    
    def _print_delay_stats(self, title: str, delays: List[float]):
        """打印延迟统计信息"""
        if not delays:
            print(f"\n{title}: 无数据")
            return
            
        avg_delay = sum(delays) / len(delays)
        min_delay = min(delays)
        max_delay = max(delays)
        
        print(f"\n{title}统计:")
        print(f"  样本数量: {len(delays)}")
        print(f"  平均延迟: {avg_delay:.2f}秒")
        print(f"  最小延迟: {min_delay:.2f}秒")
        print(f"  最大延迟: {max_delay:.2f}秒")
        
        # 分布分析
        if len(delays) > 1:
            delays_sorted = sorted(delays)
            median = delays_sorted[len(delays_sorted) // 2]
            print(f"  中位数: {median:.2f}秒")
            
            # 延迟分布
            ranges = [(0, 1), (1, 2), (2, 3), (3, 5), (5, float('inf'))]
            for start, end in ranges:
                count = len([d for d in delays if start <= d < end])
                percentage = (count / len(delays)) * 100
                range_str = f"{start}-{end}秒" if end != float('inf') else f">{start}秒"
                print(f"  {range_str}: {count}次 ({percentage:.1f}%)")
    
    def generate_report(self):
        """生成分析报告"""
        print("\n=== TTS性能分析报告 ===")
        
        # 事件统计
        event_counts = {}
        for event in self.events:
            event_type = event['type']
            event_counts[event_type] = event_counts.get(event_type, 0) + 1
        
        print("\n事件统计:")
        for event_type, count in sorted(event_counts.items()):
            print(f"  {event_type}: {count}次")
        
        # 时间线分析
        if self.events:
            start_time = self.events[0]['timestamp']
            end_time = self.events[-1]['timestamp']
            total_duration = (end_time - start_time).total_seconds()
            print(f"\n会话时长: {total_duration:.2f}秒")
            print(f"事件密度: {len(self.events) / total_duration:.2f}事件/秒")
        
        self.analyze_delays()
        
        # 优化建议
        print("\n=== 优化建议 ===")
        sentence_delays = self._analyze_sentence_delays()
        if sentence_delays:
            avg_sentence_delay = sum(sentence_delays) / len(sentence_delays)
            if avg_sentence_delay > 2.0:
                print("• 句子间延迟过长，建议实现TTS预加载机制")
            if max(sentence_delays) > 5.0:
                print("• 存在异常长的延迟，建议检查网络和API性能")
        
        generation_times = self._analyze_generation_times()
        if generation_times:
            avg_generation_time = sum(generation_times) / len(generation_times)
            if avg_generation_time > 3.0:
                print("• TTS生成时间较长，建议优化API调用或使用更快的TTS服务")


def main():
    parser = argparse.ArgumentParser(description='TTS日志分析工具')
    parser.add_argument('log_file', help='TTS日志文件路径')
    parser.add_argument('--output', '-o', help='输出报告文件路径（可选）')
    
    args = parser.parse_args()
    
    try:
        analyzer = TTSLogAnalyzer(args.log_file)
        analyzer.parse_log()
        analyzer.generate_report()
        
        if args.output:
            # 重定向输出到文件
            import sys
            with open(args.output, 'w', encoding='utf-8') as f:
                # 这里可以实现输出重定向，暂时先打印提示
                print(f"\n报告已生成，建议手动保存输出到: {args.output}")
        
    except FileNotFoundError:
        print(f"错误: 找不到日志文件 {args.log_file}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()