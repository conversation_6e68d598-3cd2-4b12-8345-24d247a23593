#!/bin/bash

#
# 一键下载并解密音频文件
#
# 功能:
# 1. 根据 task_id 从 Redis 中获取任务信息。
# 2. 提取加密音频的 URL 和解密密钥。
# 3. 下载加密的音频文件。
# 4. 使用 python 脚本和密钥进行解密。
# 5. 清理临时文件。
#
# 依赖:
# - redis-cli: 用于连接 Redis。
# - jq: 用于解析 JSON。
# - curl: 用于下载文件。
# - python3: 用于运行解密脚本。
#
# 使用方法:
# ./scripts/download_and_decrypt_audio.sh <task_id>
#
# 示例:
# ./scripts/download_and_decrypt_audio.sh 123e4567-e89b-12d3-a456-426614174000
#

set -e # 如果任何命令失败，立即退出

# --- 检查依赖 ---
command -v redis-cli >/dev/null 2>&1 || { echo >&2 "错误: redis-cli 未安装。请先安装 Redis。"; exit 1; }
command -v jq >/dev/null 2>&1 || { echo >&2 "错误: jq 未安装。请运行 'brew install jq' (macOS) 或 'sudo apt-get install jq' (Debian/Ubuntu)。"; exit 1; }
command -v curl >/dev/null 2>&1 || { echo >&2 "错误: curl 未安装。"; exit 1; }
command -v python3 >/dev/null 2>&1 || { echo >&2 "错误: python3 未安装。"; exit 1; }

# --- 脚本参数 ---
TASK_ID=$1
if [ -z "$TASK_ID" ]; then
    echo "错误: 请提供 task_id 作为第一个参数。"
    echo "使用方法: $0 <task_id>"
    exit 1
fi

# --- 变量定义 ---
REDIS_KEY="audio_summary_task:$TASK_ID"
SCRIPT_DIR=$(dirname "$0")
PROJECT_ROOT=$(realpath "$SCRIPT_DIR/..")
DECRYPT_SCRIPT="$PROJECT_ROOT/scripts/decrypt_file.py"
DOWNLOAD_DIR="$PROJECT_ROOT/tmp/decrypted_audio"

# --- 主逻辑 ---
echo "[1/5] 正在从 Redis 查询任务信息 (Key: $REDIS_KEY)..."

# 从 Redis 获取 JSON 数据
TASK_INFO_JSON=$(redis-cli GET "$REDIS_KEY")

if [ -z "$TASK_INFO_JSON" ] || [ "$TASK_INFO_JSON" == "null" ]; then
    echo "错误: 在 Redis 中未找到 Task ID '$TASK_ID' 对应的数据。"
    exit 1
fi

echo "      => 成功获取到任务信息。"

# 首先添加调试信息
echo "      => 任务信息JSON:"
echo "$TASK_INFO_JSON" | jq .
echo ""

# 使用 jq 解析 JSON
ENCRYPTED_URL=$(echo "$TASK_INFO_JSON" | jq -r '.encrypted_audio_url')
ENCRYPTION_KEY=$(echo "$TASK_INFO_JSON" | jq -r '.encryption_key')
ORIGINAL_FILENAME=$(echo "$TASK_INFO_JSON" | jq -r '.filename')
TASK_STATUS=$(echo "$TASK_INFO_JSON" | jq -r '.status')

if [ -z "$ENCRYPTED_URL" ] || [ "$ENCRYPTED_URL" == "null" ]; then
    echo "错误: 任务数据中缺少 'encrypted_audio_url'。请确保音频文件已上传到CDN。"
    exit 1
fi

if [ -z "$ENCRYPTION_KEY" ] || [ "$ENCRYPTION_KEY" == "null" ]; then
    echo "错误: 任务数据中缺少 'encryption_key'。无法解密。"
    exit 1
fi

echo "[2/5] 正在准备下载音频文件..."
echo "      - 加密音频URL: $ENCRYPTED_URL"
echo "      - 原始文件名: $ORIGINAL_FILENAME"
echo "      - 任务状态: $TASK_STATUS"

# 创建下载目录
mkdir -p "$DOWNLOAD_DIR"

# 定义文件名
ENCRYPTED_FILE="$DOWNLOAD_DIR/${TASK_ID}_audio.mp3.enc"
DECRYPTED_FILE="$DOWNLOAD_DIR/${TASK_ID}_${ORIGINAL_FILENAME}"

echo "[3/5] 正在下载加密的音频文件到: $ENCRYPTED_FILE"
curl -s -L -o "$ENCRYPTED_FILE" "$ENCRYPTED_URL"
echo "      => 下载完成。"

echo "[4/5] 正在使用 Python 脚本解密音频文件..."
echo "      - 解密脚本: $DECRYPT_SCRIPT"

# 运行解密脚本
python3 "$DECRYPT_SCRIPT" "$ENCRYPTED_FILE" "$DECRYPTED_FILE" "$ENCRYPTION_KEY"

echo "[5/5] 清理临时加密文件..."
rm "$ENCRYPTED_FILE"
echo "      => 清理完成。"

echo -e "\n🎉 全部完成！"
echo "解密后的音频文件已保存到:"
echo "$DECRYPTED_FILE"
echo "您现在可以使用任何音乐播放器打开它。"

exit 0
