#!/usr/bin/env python3
"""
简化版Redis数据提取工具
专门处理WebSocket日志中的Redis数据格式
"""

import os
import re
import csv
import json
import logging
from datetime import datetime
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleRedisExtractor:
    def __init__(self, log_directory: str, output_directory: str = "redis_simple_results"):
        self.log_directory = Path(log_directory)
        self.output_directory = Path(output_directory)
        self.output_directory.mkdir(exist_ok=True)
        
        self.user_configs = {}
        self.chat_histories = {}
        
    def parse_timestamp(self, timestamp_str: str) -> datetime:
        """解析日志时间戳"""
        try:
            return datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            return None
    
    def extract_client_id(self, text: str) -> Optional[str]:
        """提取client_id"""
        match = re.search(r'client_id=([^,\s]+)', text)
        return match.group(1) if match else None
    
    def extract_complete_json_block(self, content: str, start_marker: str) -> Optional[dict]:
        """提取完整的JSON块"""
        try:
            # 找到起始标记
            start_pos = content.find(start_marker)
            if start_pos == -1:
                return None
            
            # 找到JSON开始位置
            json_start = content.find('{', start_pos)
            if json_start == -1:
                return None
            
            # 使用简单的大括号匹配来找到JSON结束位置
            brace_count = 0
            json_end = json_start
            
            for i, char in enumerate(content[json_start:], json_start):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        json_end = i + 1
                        break
            
            if brace_count == 0:
                json_str = content[json_start:json_end]
                return json.loads(json_str)
            
            return None
            
        except (json.JSONDecodeError, ValueError) as e:
            logger.debug(f"JSON解析失败: {e}")
            return None
    
    def process_log_file(self, file_path: Path):
        """处理单个日志文件"""
        logger.info(f"处理日志文件: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 查找用户配置数据 - 使用您描述的特征进行精确匹配
        # 从 "client_id=xxx, 完整数据={" 开始，到下一个日志行（时间戳开头）前的 "}" 结束
        config_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*?client_id=([^,\s]+),\s*完整数据=(\{.*?(?=\n\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}))'
        
        for match in re.finditer(config_pattern, content, re.DOTALL):
            timestamp_str = match.group(1)
            client_id = match.group(2)
            json_block = match.group(3)
            
            timestamp = self.parse_timestamp(timestamp_str)
            
            try:
                # 清理JSON字符串
                cleaned_json = re.sub(r'\n\s*', ' ', json_block)
                cleaned_json = re.sub(r'\s+', ' ', cleaned_json)
                config_data = json.loads(cleaned_json)
                
                self.user_configs[client_id] = {
                    'timestamp': timestamp,
                    'config': config_data,
                    'file_name': file_path.name
                }
                logger.info(f"成功提取用户配置: {client_id}")
                
            except json.JSONDecodeError as e:
                logger.warning(f"用户配置JSON解析失败 {client_id}: {e}")
        
        # 查找聊天记录数据
        chat_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*?成功从Redis获取聊天记录数据.*?client_id=([^,\s]+).*?数据长度=(\d+)字符.*?原始数据=(\{.*?\})'
        
        for match in re.finditer(chat_pattern, content, re.DOTALL):
            timestamp_str = match.group(1)
            client_id = match.group(2)
            data_length = int(match.group(3))
            json_str = match.group(4)
            
            timestamp = self.parse_timestamp(timestamp_str)
            
            try:
                chat_data = json.loads(json_str)
                
                self.chat_histories[client_id] = {
                    'timestamp': timestamp,
                    'data_length': data_length,
                    'chat_data': chat_data,
                    'file_name': file_path.name
                }
                logger.info(f"成功提取聊天记录: {client_id}")
                
            except json.JSONDecodeError as e:
                logger.warning(f"聊天记录JSON解析失败 {client_id}: {e}")
    
    def generate_reports(self):
        """生成报告"""
        self.generate_user_configs_csv()
        self.generate_chat_histories_csv()
        self.generate_combined_report()
    
    def generate_user_configs_csv(self):
        """生成用户配置CSV"""
        output_file = self.output_directory / "user_configs.csv"
        
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                'Client_ID', '提取时间', '用户名', '性别', '生日', '生活方式', '兴趣爱好',
                '位置', '角色', '助手名', '最后访问时间', '最后主动时间', '配置更新时间', '来源文件'
            ])
            
            for client_id, data in self.user_configs.items():
                config = data['config']
                timestamp = data['timestamp']
                
                user_name = config.get('user_name', '')
                user_gender = config.get('user_gender', '')
                user_birth = config.get('user_birth', '')
                lifestyle = config.get('lifestyle', '')
                interests = config.get('interests', '')
                
                profile = config.get('profile', {})
                location = profile.get('location', '') if isinstance(profile, dict) else ''
                
                role = config.get('role', '')
                assistant_name = config.get('assistant_name', '')
                
                # 时间戳转换
                last_access_time = ''
                if config.get('last_access_time'):
                    try:
                        last_access_time = datetime.fromtimestamp(config['last_access_time']).strftime('%Y-%m-%d %H:%M:%S')
                    except (ValueError, TypeError):
                        pass
                
                last_proactive_time = ''
                if config.get('last_proactive_time'):
                    try:
                        last_proactive_time = datetime.fromtimestamp(config['last_proactive_time']).strftime('%Y-%m-%d %H:%M:%S')
                    except (ValueError, TypeError):
                        pass
                
                profile_last_update = ''
                if config.get('profile_last_update'):
                    try:
                        profile_last_update = datetime.fromtimestamp(config['profile_last_update']).strftime('%Y-%m-%d %H:%M:%S')
                    except (ValueError, TypeError):
                        pass
                
                writer.writerow([
                    client_id,
                    timestamp.strftime('%Y-%m-%d %H:%M:%S') if timestamp else '',
                    user_name,
                    user_gender,
                    user_birth,
                    lifestyle,
                    interests,
                    location,
                    role,
                    assistant_name,
                    last_access_time,
                    last_proactive_time,
                    profile_last_update,
                    data['file_name']
                ])
        
        logger.info(f"用户配置数据已保存到: {output_file}")
    
    def generate_chat_histories_csv(self):
        """生成聊天记录CSV"""
        output_file = self.output_directory / "chat_histories.csv"
        
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                'Client_ID', '提取时间', '数据长度(字符)', '聊天摘要', '最后更新时间', '来源文件'
            ])
            
            for client_id, data in self.chat_histories.items():
                chat_data = data['chat_data']
                timestamp = data['timestamp']
                data_length = data['data_length']
                
                summary = chat_data.get('summary', '')
                
                last_update_time = ''
                if chat_data.get('last_update_time'):
                    try:
                        last_update_time = datetime.fromtimestamp(chat_data['last_update_time']).strftime('%Y-%m-%d %H:%M:%S')
                    except (ValueError, TypeError):
                        pass
                
                writer.writerow([
                    client_id,
                    timestamp.strftime('%Y-%m-%d %H:%M:%S') if timestamp else '',
                    data_length,
                    summary,
                    last_update_time,
                    data['file_name']
                ])
        
        logger.info(f"聊天记录数据已保存到: {output_file}")
    
    def generate_combined_report(self):
        """生成综合报告"""
        output_file = self.output_directory / "combined_user_data.csv"
        
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                'Client_ID', '用户名', '角色', '助手名', '位置', '生活方式', '兴趣爱好',
                '聊天记录长度', '聊天摘要', '完整配置JSON'
            ])
            
            # 合并配置和聊天数据
            all_clients = set(self.user_configs.keys()) | set(self.chat_histories.keys())
            
            for client_id in all_clients:
                config_data = self.user_configs.get(client_id, {})
                chat_data = self.chat_histories.get(client_id, {})
                
                config = config_data.get('config', {})
                chat_info = chat_data.get('chat_data', {})
                
                user_name = config.get('user_name', '')
                role = config.get('role', '')
                assistant_name = config.get('assistant_name', '')
                
                profile = config.get('profile', {})
                location = profile.get('location', '') if isinstance(profile, dict) else ''
                
                lifestyle = config.get('lifestyle', '')
                interests = config.get('interests', '')
                
                chat_length = chat_data.get('data_length', 0)
                chat_summary = chat_info.get('summary', '')
                
                config_json = json.dumps(config, ensure_ascii=False, indent=2) if config else ''
                
                writer.writerow([
                    client_id,
                    user_name,
                    role,
                    assistant_name,
                    location,
                    lifestyle,
                    interests,
                    chat_length,
                    chat_summary,
                    config_json
                ])
        
        logger.info(f"综合报告已保存到: {output_file}")
    
    def extract_data(self):
        """执行数据提取"""
        logger.info("开始提取Redis数据...")
        
        # 查找所有日志文件
        log_files = []
        for root, dirs, files in os.walk(self.log_directory):
            for file in files:
                if file.endswith('.log'):
                    log_files.append(Path(root) / file)
        
        logger.info(f"找到 {len(log_files)} 个日志文件")
        
        # 处理每个日志文件
        for log_file in log_files:
            self.process_log_file(log_file)
        
        # 生成报告
        logger.info("生成CSV报告...")
        self.generate_reports()
        
        logger.info(f"提取完成！报告已保存到: {self.output_directory}")
        logger.info(f"共提取了 {len(self.user_configs)} 个用户配置")
        logger.info(f"共提取了 {len(self.chat_histories)} 个聊天记录")
        
        # 打印统计信息
        self.print_statistics()
    
    def print_statistics(self):
        """打印统计信息"""
        logger.info("\n=== 数据统计 ===")
        
        if self.user_configs:
            roles = {}
            locations = {}
            for client_id, data in self.user_configs.items():
                config = data['config']
                role = config.get('role', '未设置')
                roles[role] = roles.get(role, 0) + 1
                
                profile = config.get('profile', {})
                location = profile.get('location', '未设置') if isinstance(profile, dict) else '未设置'
                locations[location] = locations.get(location, 0) + 1
            
            logger.info(f"角色分布: {dict(sorted(roles.items(), key=lambda x: x[1], reverse=True))}")
            logger.info(f"地域分布: {dict(sorted(locations.items(), key=lambda x: x[1], reverse=True))}")
        
        if self.chat_histories:
            lengths = [data['data_length'] for data in self.chat_histories.values()]
            if lengths:
                avg_length = sum(lengths) / len(lengths)
                logger.info(f"平均聊天记录长度: {avg_length:.1f} 字符")
                logger.info(f"最长聊天记录: {max(lengths)} 字符")
                logger.info(f"最短聊天记录: {min(lengths)} 字符")

def main():
    """主函数"""
    log_directory = "/Users/<USER>/Downloads/log"
    
    if not os.path.exists(log_directory):
        logger.error(f"日志目录不存在: {log_directory}")
        return
    
    extractor = SimpleRedisExtractor(log_directory)
    extractor.extract_data()

if __name__ == "__main__":
    main()