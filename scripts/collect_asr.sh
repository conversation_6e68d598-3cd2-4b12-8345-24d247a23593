#!/bin/bash

# 清理旧的ASR文件
echo "Cleaning up old ASR files..."
rm -f asr_*.wav asr_*.mp3 asr_*.pcm

# 根据主机名设置容器数量
HOSTNAME=$(hostname)
if [[ $HOSTNAME == gpu001* ]]; then
  echo "Hostname '$HOSTNAME': collecting ASR files from 3 containers."
  CONTAINER_COUNT=3
else
  echo "Hostname '$HOSTNAME': collecting ASR files from 8 containers."
  CONTAINER_COUNT=8
fi

# 循环复制ASR文件
ASR_FILES=""
for i in $(seq 1 $CONTAINER_COUNT); do
  CONTAINER_NAME="xiaozhi-esp32-server-$i"
  echo "Copying ASR files from $CONTAINER_NAME..."
  
  # 检查容器是否在运行
  if ! sudo docker ps --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
    echo "Warning: Container $CONTAINER_NAME is not running. Skipping."
    continue
  fi
  
  # 获取容器中的ASR文件列表
  ASR_FILE_LIST=$(sudo docker exec "$CONTAINER_NAME" find /tmp -name "asr_*" -type f 2>/dev/null)
  
  if [ -z "$ASR_FILE_LIST" ]; then
    echo "No ASR files found in $CONTAINER_NAME."
    continue
  fi
  
  # 复制每个ASR文件
  for asr_file in $ASR_FILE_LIST; do
    # 获取文件名
    filename=$(basename "$asr_file")
    # 添加容器编号前缀避免文件名冲突
    local_filename="container${i}_${filename}"
    
    echo "  Copying $asr_file to $local_filename..."
    sudo docker cp "$CONTAINER_NAME:$asr_file" "$local_filename"
    
    if [ $? -eq 0 ]; then
      ASR_FILES="$ASR_FILES $local_filename"
    else
      echo "  Warning: Failed to copy $asr_file from $CONTAINER_NAME."
    fi
  done
done

# 检查是否有ASR文件被成功复制
if [ -z "$ASR_FILES" ]; then
  echo "No ASR files were collected. Aborting zip."
  exit 1
fi

# 压缩ASR文件
ZIP_FILE="/tmp/asr_files.zip"
echo "Zipping ASR files to $ZIP_FILE..."
rm -f "$ZIP_FILE"
zip "$ZIP_FILE" $ASR_FILES

# 显示文件统计信息
echo "ASR file collection summary:"
echo "  Total files collected: $(echo $ASR_FILES | wc -w)"
echo "  Zip file size: $(du -h "$ZIP_FILE" | cut -f1)"

# 清理本地的ASR文件
echo "Cleaning up temporary ASR files..."
rm -f $ASR_FILES

echo "ASR file collection complete. The zip file is located at: $ZIP_FILE"