#!/usr/bin/env python3
import argparse
import sys
import os

# Add project root to the Python path to allow importing 'core'
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from core.utils.crypto import xor_decrypt

def main():
    """
    使用XOR密钥解密文件。
    """
    parser = argparse.ArgumentParser(
        description="使用XOR密钥解密由audio_processor.py加密的文件。",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("input_file", help="要解密的输入文件路径 (.enc)")
    parser.add_argument("output_file", help="解密后输出的文件路径 (.mp3, .md, etc.)")
    parser.add_argument("key", help="十六进制格式的解密密钥")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_file):
        print(f"[!] 错误: 输入文件未找到 -> {args.input_file}", file=sys.stderr)
        sys.exit(1)
        
    print(f"[*] 开始解密文件: {args.input_file}")
    
    try:
        # 读取加密文件
        with open(args.input_file, 'rb') as f:
            encrypted_data = f.read()
        
        print(f"[*] 读取 {len(encrypted_data)} 字节数据")
        
        # 解密
        decrypted_data = xor_decrypt(encrypted_data, args.key)
        print(f"[*] 解密完成")
        
        # 写入解密后的文件
        with open(args.output_file, 'wb') as f:
            f.write(decrypted_data)
            
        print(f"\n[+] 成功！解密后的文件已保存到: {args.output_file}")
        
    except Exception as e:
        print(f"[!] 发生错误: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
