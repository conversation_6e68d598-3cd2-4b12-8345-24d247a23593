#!/usr/bin/env python3
"""
WebSocket日志分析工具
分析WebSocket服务器日志，提取用户会话数据和延迟统计信息
输出CSV格式便于Excel查看
"""

import os
import re
import csv
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Tuple, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LogAnalyzer:
    def __init__(self, log_directory: str, output_directory: str = "analysis_results"):
        self.log_directory = Path(log_directory)
        self.output_directory = Path(output_directory)
        self.output_directory.mkdir(exist_ok=True)
        
        # 存储解析的数据
        self.sessions = {}  # unique_session_key -> session_data
        self.client_to_session_map = {}  # client_tag -> unique_session_key
        self.log_sources = {}  # 存储各种数据的日志来源
        self.daily_stats = defaultdict(lambda: {
            'unique_ips': set(),
            'unique_clients': set(),
            'total_conversations': 0,
            'session_durations': []
        })
        self.conversations = []  # 对话延迟明细
        self.heartbeats = []     # 心跳详细记录
        self.tts_parallel = []   # TTS并行分析
        
    def parse_timestamp(self, timestamp_str: str) -> datetime:
        """解析日志时间戳"""
        try:
            return datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                # 尝试带毫秒的格式
                return datetime.strptime(timestamp_str.split('.')[0], "%Y-%m-%d %H:%M:%S")
            except ValueError:
                return None
    
    def extract_client_info(self, line: str) -> Tuple[Optional[str], Optional[str]]:
        """从连接日志中提取客户端ID和IP"""
        client_id_match = re.search(r"'client-id': '([^']+)'", line)
        ip_match = re.search(r"'x-real-ip': '([^']+)'", line)
        
        client_id = client_id_match.group(1) if client_id_match else None
        ip = ip_match.group(1) if ip_match else None
        
        return client_id, ip
    
    def extract_session_id(self, line: str) -> Optional[str]:
        """提取session_id"""
        session_match = re.search(r'"session_id":\s*"([^"]+)"', line)
        return session_match.group(1) if session_match else None
    
    def extract_json_message(self, line: str) -> Optional[dict]:
        """提取JSON消息"""
        json_match = re.search(r'收到文本消息：(\{.*\})', line)
        if json_match:
            try:
                return json.loads(json_match.group(1))
            except json.JSONDecodeError:
                return None
        return None
    
    def extract_tts_text(self, line: str) -> Optional[str]:
        """提取TTS文本"""
        # 原格式：语音生成成功: 文本内容:
        tts_match = re.search(r'语音生成成功: ([^:]+):', line)
        if tts_match:
            return tts_match.group(1)
        
        # 新格式：开始处理TTS任务 sentence_X (优先级X): 文本内容
        new_tts_match = re.search(r'开始处理TTS任务 sentence_\d+ \(优先级\d+\): (.+)', line)
        if new_tts_match:
            text = new_tts_match.group(1).replace('...', '').strip()
            # 移除可能的末尾省略号
            if text.endswith('...'):
                text = text[:-3].strip()
            return text
        
        return None
    
    def handle_tts_generation_complete(self, timestamp: datetime, line: str, client_tag: str, file_name: str = None):
        """处理TTS生成完成日志"""
        if not client_tag:
            return
            
        session_key = self.client_to_session_map.get(client_tag)
        if not session_key or session_key not in self.sessions:
            return
        
        # 提取sentence号码
        sentence_match = re.search(r'sentence_(\d+)', line)
        sentence_number = int(sentence_match.group(1)) if sentence_match else None
        
        # 提取耗时
        duration_match = re.search(r'耗时: ([\d.]+)s', line)
        duration = float(duration_match.group(1)) if duration_match else None
        
        if sentence_number:
            conversations = self.sessions[session_key].get('conversations', [])
            if conversations and 'tts_generations' in conversations[-1]:
                tts_gens = conversations[-1]['tts_generations']
                
                # 找到对应的TTS记录并更新
                for tts_gen in tts_gens:
                    if tts_gen.get('sentence_number') == sentence_number:
                        current_file = file_name or self.sessions[session_key].get('file_name', 'unknown')
                        tts_gen.update({
                            'generation_complete_time': timestamp,
                            'generation_duration': duration,
                            'generation_complete_log_source': f"从 {current_file}: {line.strip()}"
                        })
                        logger.debug(f"TTS生成完成 sentence_{sentence_number}, 耗时: {duration}秒")
                        break
    
    def extract_asr_result(self, line: str) -> Tuple[Optional[float], Optional[str]]:
        """提取语音识别结果和耗时"""
        asr_match = re.search(r'语音识别耗时: ([\d.]+)s \| 结果: (.+)', line)
        if asr_match:
            duration = float(asr_match.group(1))
            text = asr_match.group(2)
            return duration, text
        return None, None
    
    def extract_llm_sentence(self, line: str) -> Optional[str]:
        """提取LLM输出的句子"""
        sentence_match = re.search(r'大模型说出第一句话: (.+)', line)
        return sentence_match.group(1) if sentence_match else None
    
    def extract_tts_playback_time(self, line: str) -> Optional[float]:
        """提取TTS播放时间"""
        # 原格式：句子 X 音频播放完成，耗时: Xms
        playback_match = re.search(r'句子 \d+ 音频播放完成，耗时: ([\d.]+)ms', line)
        if playback_match:
            return float(playback_match.group(1)) / 1000.0  # 转换为秒
        
        # 新格式：音频播放完成 sentence_X, 播放时长: X.XXs
        new_playback_match = re.search(r'音频播放完成 sentence_\d+, 播放时长: ([\d.]+)s', line)
        if new_playback_match:
            return float(new_playback_match.group(1))
        
        return None
    
    def parse_log_file(self, file_path: Path):
        """解析单个日志文件"""
        logger.info(f"解析日志文件: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    self.parse_log_line(line.strip(), file_path.name)
                except Exception as e:
                    logger.warning(f"解析第{line_num}行时出错: {e}")
    
    def parse_log_line(self, line: str, file_name: str):
        """解析单行日志"""
        if not line:
            return
            
        # 提取时间戳
        timestamp_match = re.match(r'^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
        if not timestamp_match:
            return
            
        timestamp_str = timestamp_match.group(1)
        timestamp = self.parse_timestamp(timestamp_str)
        if not timestamp:
            return
            
        date_str = timestamp.date().strftime('%Y-%m-%d')
        
        # 提取客户端标识
        client_match = re.search(r' - ([^-\s]+) - ', line)
        client_tag = client_match.group(1) if client_match else None
        
        # 连接建立
        if 'connection is OPEN' in line:
            self.handle_connection_open(timestamp, client_tag, file_name)
        
        # 连接信息
        elif 'conn - Headers:' in line:
            self.handle_connection_headers(timestamp, line, client_tag, file_name)
        
        # Hello握手
        elif '收到文本消息：' in line and '"type":"hello"' in line:
            self.handle_hello_message(timestamp, line, client_tag, file_name)
        
        # 收到Hi~
        elif '收到文本消息：' in line and '"text":"Hi~"' in line:
            self.handle_hi_message(timestamp, line, client_tag, file_name)
        
        # 用户开始说话
        elif '收到文本消息：' in line and '"state":"start"' in line:
            self.handle_user_start_speaking(timestamp, line, client_tag, file_name)
        
        # 语音识别完成
        elif '语音识别耗时:' in line:
            self.handle_asr_complete(timestamp, line, client_tag, file_name)
        
        # 审核完成
        elif 'question内容检测通过' in line:
            self.handle_policy_check(timestamp, line, client_tag, file_name)
        
        # LLM首字符响应
        elif '收到第1个LLM响应' in line:
            self.handle_llm_first_token(timestamp, client_tag, file_name)
        
        # LLM句子完成
        elif '大模型说出第一句话:' in line:
            self.handle_llm_sentence(timestamp, line, client_tag, file_name)
        
        # TTS生成完成
        elif '语音生成成功:' in line:
            self.handle_tts_generation(timestamp, line, client_tag, file_name)
        
        # 新的TTS任务开始（用于提取TTS文本）
        elif '开始处理TTS任务 sentence_' in line:
            self.handle_tts_generation(timestamp, line, client_tag, file_name)
        
        # TTS播放完成
        elif '音频播放完成，耗时:' in line or '音频播放完成 sentence_' in line or '句子' in line and '音频播放完成' in line:
            self.handle_tts_playback(timestamp, line, client_tag, file_name)
        
        # PING发送
        elif '> PING' in line or 'sending keepalive ping' in line:
            self.handle_ping_sent(timestamp, client_tag)
        
        # PONG接收
        elif '< PONG' in line or 'received keepalive pong' in line:
            self.handle_pong_received(timestamp, client_tag)
        
        # 客户端断开连接
        elif '客户端断开连接' in line:
            self.handle_client_disconnect(timestamp, line, client_tag, file_name)
    
    def get_session_key(self, client_tag: str, client_id: str = None, session_id: str = None) -> str:
        """生成唯一会话标识"""
        if client_id and session_id:
            return f"{client_id}_{session_id}"
        elif client_id:
            return f"{client_id}_{client_tag}"
        else:
            return client_tag
    
    def handle_connection_open(self, timestamp: datetime, client_tag: str, file_name: str):
        """处理连接建立事件"""
        if not client_tag:
            return
            
        # 初始化会话数据
        session_key = self.get_session_key(client_tag)
        if session_key not in self.sessions:
            self.sessions[session_key] = {
                'client_tag': client_tag,
                'connection_open_time': timestamp,
                'file_name': file_name,
                'ping_times': [],
                'pong_times': [],
                'conversations': [],
                'log_sources': {
                    'connection_open': f"{file_name}: connection is OPEN"
                }
            }
            logger.debug(f"创建新会话: {session_key}, 连接时间: {timestamp} (文件: {file_name})")
        self.client_to_session_map[client_tag] = session_key
    
    def handle_connection_headers(self, timestamp: datetime, line: str, client_tag: str, file_name: str = None):
        """处理连接头信息"""
        if not client_tag:
            return
            
        client_id, ip = self.extract_client_info(line)
        if client_id and ip:
            # 更新会话标识
            new_session_key = self.get_session_key(client_tag, client_id)
            old_session_key = self.client_to_session_map.get(client_tag, client_tag)
            
            # 如果需要，迁移数据到新的session_key
            if old_session_key != new_session_key and old_session_key in self.sessions:
                self.sessions[new_session_key] = self.sessions.pop(old_session_key)
                self.client_to_session_map[client_tag] = new_session_key
            
            session_key = self.client_to_session_map.get(client_tag, new_session_key)
            if session_key not in self.sessions:
                self.sessions[session_key] = {
                    'client_tag': client_tag,
                    'connection_open_time': timestamp,
                    'ping_times': [],
                    'pong_times': [],
                    'conversations': []
                }
            
            self.sessions[session_key].update({
                'client_id': client_id,
                'ip': ip,
                'headers_time': timestamp
            })
            
            # 记录数据来源
            if 'log_sources' not in self.sessions[session_key]:
                self.sessions[session_key]['log_sources'] = {}
            current_file = file_name or self.sessions[session_key].get('file_name', 'unknown')
            self.sessions[session_key]['log_sources'].update({
                'client_id': f"从 {current_file}: Headers: 'client-id': '{client_id}'",
                'ip': f"从 {current_file}: Headers: 'x-real-ip': '{ip}'"
            })
            
            logger.debug(f"提取到客户端信息: client_id={client_id}, ip={ip} (客户端: {client_tag})")
            
            # 更新每日统计
            date_str = timestamp.date().strftime('%Y-%m-%d')
            self.daily_stats[date_str]['unique_ips'].add(ip)
            self.daily_stats[date_str]['unique_clients'].add(client_id)
    
    def handle_hello_message(self, timestamp: datetime, line: str, client_tag: str, file_name: str = None):
        """处理Hello握手"""
        if not client_tag:
            return
            
        session_key = self.client_to_session_map.get(client_tag)
        if not session_key or session_key not in self.sessions:
            return
            
        session_id = self.extract_session_id(line)
        if session_id:
            # 更新session_id，但保持原有的session_key不变以避免数据丢失
            self.sessions[session_key].update({
                'session_id': session_id,
                'hello_time': timestamp
            })
            
            # 记录数据来源
            if 'log_sources' not in self.sessions[session_key]:
                self.sessions[session_key]['log_sources'] = {}
            current_file = file_name or self.sessions[session_key].get('file_name', 'unknown')
            self.sessions[session_key]['log_sources']['session_id'] = f"从 {current_file}: 收到文本消息：{{\"type\":\"hello\",\"session_id\":\"{session_id}\"}}"
            
            logger.debug(f"提取到session_id: {session_id} 从 hello消息 (客户端: {client_tag})")
    
    def handle_hi_message(self, timestamp: datetime, line: str, client_tag: str, file_name: str = None):
        """处理收到Hi~消息"""
        if not client_tag:
            return
            
        session_key = self.client_to_session_map.get(client_tag)
        if not session_key or session_key not in self.sessions:
            return
            
        self.sessions[session_key]['hi_received_time'] = timestamp
    
    def handle_user_start_speaking(self, timestamp: datetime, line: str, client_tag: str, file_name: str = None):
        """处理用户开始说话"""
        if not client_tag:
            return
            
        session_key = self.client_to_session_map.get(client_tag)
        if not session_key or session_key not in self.sessions:
            return
            
        # 开始新的对话
        current_file = file_name or self.sessions[session_key].get('file_name', 'unknown')
        conversation = {
            'start_time': timestamp,
            'sequence': len(self.sessions[session_key].get('conversations', [])) + 1,
            'log_sources': {
                'start_time': f"从 {current_file}: 收到文本消息：\"state\":\"start\"",
                'file_name': current_file
            }
        }
        
        if 'conversations' not in self.sessions[session_key]:
            self.sessions[session_key]['conversations'] = []
        
        self.sessions[session_key]['conversations'].append(conversation)
    
    def handle_asr_complete(self, timestamp: datetime, line: str, client_tag: str, file_name: str = None):
        """处理语音识别完成"""
        if not client_tag:
            return
            
        session_key = self.client_to_session_map.get(client_tag)
        if not session_key or session_key not in self.sessions:
            return
            
        asr_duration, user_text = self.extract_asr_result(line)
        if asr_duration is not None and user_text:
            # 更新最后一个对话
            conversations = self.sessions[session_key].get('conversations', [])
            if conversations:
                current_file = file_name or self.sessions[session_key].get('file_name', 'unknown')
                if 'log_sources' not in conversations[-1]:
                    conversations[-1]['log_sources'] = {}
                conversations[-1]['log_sources']['asr'] = f"从 {current_file}: 语音识别耗时: {asr_duration}s | 结果: {user_text}"
                
                conversations[-1].update({
                    'asr_complete_time': timestamp,
                    'asr_duration': asr_duration,
                    'user_input': user_text
                })
    
    def handle_policy_check(self, timestamp: datetime, line: str, client_tag: str, file_name: str = None):
        """处理审核完成"""
        if not client_tag:
            return
            
        session_key = self.client_to_session_map.get(client_tag)
        if not session_key or session_key not in self.sessions:
            return
            
        conversations = self.sessions[session_key].get('conversations', [])
        if conversations:
            current_file = file_name or self.sessions[session_key].get('file_name', 'unknown')
            if 'log_sources' not in conversations[-1]:
                conversations[-1]['log_sources'] = {}
            conversations[-1]['log_sources']['policy'] = f"从 {current_file}: question内容检测通过"
            conversations[-1]['policy_check_time'] = timestamp
    
    def handle_llm_first_token(self, timestamp: datetime, client_tag: str, file_name: str = None):
        """处理LLM首字符响应"""
        if not client_tag:
            return
            
        session_key = self.client_to_session_map.get(client_tag)
        if not session_key or session_key not in self.sessions:
            return
            
        conversations = self.sessions[session_key].get('conversations', [])
        if conversations:
            current_file = file_name or self.sessions[session_key].get('file_name', 'unknown')
            if 'log_sources' not in conversations[-1]:
                conversations[-1]['log_sources'] = {}
            conversations[-1]['log_sources']['llm_first_token'] = f"从 {current_file}: 收到第1个LLM响应"
            conversations[-1]['llm_first_token_time'] = timestamp
    
    def handle_llm_sentence(self, timestamp: datetime, line: str, client_tag: str, file_name: str = None):
        """处理LLM句子完成"""
        if not client_tag:
            return
            
        session_key = self.client_to_session_map.get(client_tag)
        if not session_key or session_key not in self.sessions:
            return
            
        sentence = self.extract_llm_sentence(line)
        if sentence:
            conversations = self.sessions[session_key].get('conversations', [])
            if conversations:
                current_file = file_name or self.sessions[session_key].get('file_name', 'unknown')
                if 'log_sources' not in conversations[-1]:
                    conversations[-1]['log_sources'] = {}
                conversations[-1]['log_sources']['first_sentence'] = f"从 {current_file}: 大模型说出第一句话: {sentence}"
                
                conversations[-1].update({
                    'first_sentence_time': timestamp,
                    'first_sentence': sentence
                })
    
    def handle_tts_generation(self, timestamp: datetime, line: str, client_tag: str, file_name: str = None):
        """处理TTS生成完成"""
        if not client_tag:
            return
            
        session_key = self.client_to_session_map.get(client_tag)
        if not session_key or session_key not in self.sessions:
            return
            
        tts_text = self.extract_tts_text(line)
        if tts_text:
            # 提取sentence号码（如果是新格式）
            sentence_match = re.search(r'sentence_(\d+)', line)
            sentence_number = int(sentence_match.group(1)) if sentence_match else None
            
            # 判断是否为Hi的TTS
            if 'conversations' not in self.sessions[session_key] or not self.sessions[session_key]['conversations']:
                # 这是Hi的TTS
                self.sessions[session_key]['hi_tts_generation_time'] = timestamp
                self.sessions[session_key]['hi_tts_text'] = tts_text
            else:
                # 这是对话中的TTS
                conversations = self.sessions[session_key]['conversations']
                if conversations:
                    if 'tts_generations' not in conversations[-1]:
                        conversations[-1]['tts_generations'] = []
                    
                    current_file = file_name or self.sessions[session_key].get('file_name', 'unknown')
                    tts_log_source = f"从 {current_file}: {line.strip()}"
                    
                    conversations[-1]['tts_generations'].append({
                        'text': tts_text,
                        'generation_time': timestamp,
                        'sentence_number': sentence_number or (len(conversations[-1]['tts_generations']) + 1),
                        'log_source': tts_log_source
                    })
    
    def handle_tts_playback(self, timestamp: datetime, line: str, client_tag: str, file_name: str = None):
        """处理TTS播放完成"""
        if not client_tag:
            return
            
        session_key = self.client_to_session_map.get(client_tag)
        if not session_key or session_key not in self.sessions:
            return
            
        playback_duration = self.extract_tts_playback_time(line)
        if playback_duration is not None:
            # 提取sentence号码（如果是新格式）
            sentence_match = re.search(r'sentence_(\d+)', line)
            sentence_number = int(sentence_match.group(1)) if sentence_match else None
            
            # 判断是否为Hi的TTS播放（检查是否是句子 0）
            sentence_match = re.search(r'句子 (\d+)', line)
            sentence_number_old = int(sentence_match.group(1)) if sentence_match else None
            
            # 如果是句子 0 或者没有对话，认为是Hi的TTS
            if (sentence_number_old == 0) or ('conversations' not in self.sessions[session_key] or not self.sessions[session_key]['conversations']):
                # 这是Hi的TTS播放
                self.sessions[session_key]['hi_tts_playback_time'] = timestamp
                self.sessions[session_key]['hi_tts_playback_duration'] = playback_duration
            else:
                # 这是对话中的TTS播放
                conversations = self.sessions[session_key]['conversations']
                if conversations and 'tts_generations' in conversations[-1]:
                    tts_gens = conversations[-1]['tts_generations']
                    
                    current_file = file_name or self.sessions[session_key].get('file_name', 'unknown')
                    playback_log_source = f"从 {current_file}: {line.strip()}"
                    
                    # 如果有sentence号码，找到对应的TTS生成记录
                    if sentence_number:
                        for tts_gen in tts_gens:
                            if tts_gen.get('sentence_number') == sentence_number:
                                tts_gen.update({
                                    'playback_time': timestamp,
                                    'playback_duration': playback_duration,
                                    'playback_log_source': playback_log_source
                                })
                                break
                    elif tts_gens:
                        # 如果没有sentence号码，更新最后一个
                        tts_gens[-1].update({
                            'playback_time': timestamp,
                            'playback_duration': playback_duration,
                            'playback_log_source': playback_log_source
                        })
    
    def handle_ping_sent(self, timestamp: datetime, client_tag: str):
        """处理PING发送"""
        if not client_tag:
            return
            
        session_key = self.client_to_session_map.get(client_tag)
        if not session_key or session_key not in self.sessions:
            return
            
        self.sessions[session_key]['ping_times'].append(timestamp)
    
    def handle_pong_received(self, timestamp: datetime, client_tag: str):
        """处理PONG接收"""
        if not client_tag:
            return
            
        session_key = self.client_to_session_map.get(client_tag)
        if not session_key or session_key not in self.sessions:
            return
            
        self.sessions[session_key]['pong_times'].append(timestamp)
    
    def handle_client_disconnect(self, timestamp: datetime, line: str, client_tag: str, file_name: str = None):
        """处理客户端断开连接"""
        if not client_tag:
            return
            
        session_key = self.client_to_session_map.get(client_tag)
        if not session_key or session_key not in self.sessions:
            return
        
        # 提取会话ID和连接时长
        session_id_match = re.search(r'会话ID: ([^,]+)', line)
        duration_match = re.search(r'连接时长: ([\d.]+)秒', line)
        
        if session_id_match:
            extracted_session_id = session_id_match.group(1)
            self.sessions[session_key]['extracted_session_id'] = extracted_session_id
            
            # 记录数据来源
            if 'log_sources' not in self.sessions[session_key]:
                self.sessions[session_key]['log_sources'] = {}
            current_file = file_name or self.sessions[session_key].get('file_name', 'unknown')
            if 'session_id' not in self.sessions[session_key]['log_sources']:
                self.sessions[session_key]['log_sources']['session_id'] = f"从 {current_file}: 客户端断开连接 - 会话ID: {extracted_session_id}"
            
            logger.debug(f"从断开连接日志提取到session_id: {extracted_session_id} (客户端: {client_tag})")
        
        if duration_match:
            logged_duration = float(duration_match.group(1))
            self.sessions[session_key]['logged_duration'] = logged_duration
            
            # 记录数据来源
            if 'log_sources' not in self.sessions[session_key]:
                self.sessions[session_key]['log_sources'] = {}
            current_file = file_name or self.sessions[session_key].get('file_name', 'unknown')
            self.sessions[session_key]['log_sources']['duration'] = f"从 {current_file}: 客户端断开连接 - 连接时长: {logged_duration}秒"
            
            logger.debug(f"从断开连接日志提取到时长: {logged_duration}秒 (客户端: {client_tag})")
        
        # 设置断开连接时间
        self.sessions[session_key]['disconnect_time'] = timestamp
        
        # 记录数据来源
        if 'log_sources' not in self.sessions[session_key]:
            self.sessions[session_key]['log_sources'] = {}
        current_file = file_name or self.sessions[session_key].get('file_name', 'unknown')
        self.sessions[session_key]['log_sources']['disconnect_time'] = f"从 {current_file}: 客户端断开连接"
    
    def calculate_session_metrics(self):
        """计算会话指标"""
        for client_tag, session in self.sessions.items():
            if 'client_id' not in session:
                continue
                
            # 计算各种延迟
            connection_time = session.get('connection_open_time')
            hello_time = session.get('hello_time')
            hi_time = session.get('hi_received_time')
            hi_tts_gen_time = session.get('hi_tts_generation_time')
            hi_tts_play_time = session.get('hi_tts_playback_time')
            
            # 连接到Hello的延迟
            if connection_time and hello_time:
                session['connection_to_hello_delay'] = (hello_time - connection_time).total_seconds()
            
            # Hello到Hi的延迟
            if hello_time and hi_time:
                session['hello_to_hi_delay'] = (hi_time - hello_time).total_seconds()
            
            # Hi的TTS生成时间
            if hi_time and hi_tts_gen_time:
                session['hi_tts_generation_delay'] = (hi_tts_gen_time - hi_time).total_seconds()
            
            # Hi的TTS播放时间
            if hi_tts_gen_time and hi_tts_play_time:
                session['hi_tts_playback_delay'] = (hi_tts_play_time - hi_tts_gen_time).total_seconds()
            
            # 连接到首个PING的间隔
            ping_times = session.get('ping_times', [])
            if connection_time and ping_times:
                session['connection_to_first_ping'] = (ping_times[0] - connection_time).total_seconds()
            
            # 计算对话指标
            conversations = session.get('conversations', [])
            for conv in conversations:
                self.calculate_conversation_metrics(conv, session)
            
            # 计算会话时长并更新每日统计
            if connection_time:
                date_str = connection_time.date().strftime('%Y-%m-%d')
                
                # 优先使用断开连接时间作为结束时间
                end_time = session.get('disconnect_time')
                calculated_duration = 0
                logged_duration = session.get('logged_duration')
                
                # 如果没有断开连接日志，则估算结束时间
                if not end_time:
                    if conversations:
                        # 找到最后一个对话的最新时间
                        last_conv = conversations[-1]
                        end_candidates = []
                        
                        # 添加各个处理步骤的时间
                        if last_conv.get('start_time'):
                            end_candidates.append(last_conv['start_time'])
                        if last_conv.get('asr_complete_time'):
                            end_candidates.append(last_conv['asr_complete_time'])
                        if last_conv.get('policy_check_time'):
                            end_candidates.append(last_conv['policy_check_time'])
                        if last_conv.get('llm_first_token_time'):
                            end_candidates.append(last_conv['llm_first_token_time'])
                        if last_conv.get('first_sentence_time'):
                            end_candidates.append(last_conv['first_sentence_time'])
                        
                        # 检查TTS生成和播放时间
                        tts_generations = last_conv.get('tts_generations', [])
                        for tts_gen in tts_generations:
                            if tts_gen.get('generation_time'):
                                end_candidates.append(tts_gen['generation_time'])
                            if tts_gen.get('playback_time'):
                                end_candidates.append(tts_gen['playback_time'])
                        
                        if end_candidates:
                            end_time = max(end_candidates)
                    
                    # 如果没有对话，使用Hi相关的时间戳
                    if not end_time:
                        end_candidates = []
                        if hi_tts_play_time:
                            end_candidates.append(hi_tts_play_time)
                        elif hi_tts_gen_time:
                            end_candidates.append(hi_tts_gen_time)
                        elif hi_time:
                            end_candidates.append(hi_time)
                        elif hello_time:
                            end_candidates.append(hello_time)
                        
                        if end_candidates:
                            end_time = max(end_candidates)
                
                # 计算会话时长并进行交叉核对
                if end_time and end_time > connection_time:
                    calculated_duration = (end_time - connection_time).total_seconds()
                    
                    # 交叉核对时长
                    final_duration = calculated_duration
                    if logged_duration is not None:
                        duration_diff = abs(calculated_duration - logged_duration)
                        if duration_diff > 1.0:  # 如果差异超过1秒，显示警告
                            logger.warning(f"会话 {session.get('client_id', client_tag)} 时长不一致: 计算值={calculated_duration:.2f}s, 日志值={logged_duration:.2f}s, 差异={duration_diff:.2f}s")
                        # 优先使用日志中的时长
                        final_duration = logged_duration
                    
                    session['calculated_duration'] = calculated_duration
                    session['final_duration'] = final_duration
                    self.daily_stats[date_str]['session_durations'].append(final_duration)
                
                # 更新对话数统计
                self.daily_stats[date_str]['total_conversations'] += len(conversations)
    
    def calculate_conversation_metrics(self, conversation: dict, session: dict):
        """计算单个对话的指标"""
        start_time = conversation.get('start_time')
        asr_complete_time = conversation.get('asr_complete_time')
        policy_check_time = conversation.get('policy_check_time')
        llm_first_token_time = conversation.get('llm_first_token_time')
        first_sentence_time = conversation.get('first_sentence_time')
        
        # 语音识别时间（已在解析时获得）
        # 审核时间
        if asr_complete_time and policy_check_time:
            conversation['policy_check_delay'] = (policy_check_time - asr_complete_time).total_seconds()
        
        # LLM首字符响应时间
        if policy_check_time and llm_first_token_time:
            conversation['llm_first_token_delay'] = (llm_first_token_time - policy_check_time).total_seconds()
        
        # 首句延迟
        if start_time and first_sentence_time:
            conversation['first_sentence_delay'] = (first_sentence_time - start_time).total_seconds()
    
    def generate_csv_reports(self):
        """生成CSV报告"""
        self.generate_daily_summary()
        self.generate_client_sessions()
        self.generate_conversation_latencies()
        self.generate_heartbeat_details()
        self.generate_tts_parallel_analysis()
    
    def generate_daily_summary(self):
        """生成每日统计汇总"""
        output_file = self.output_directory / "daily_summary.csv"
        
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['日期', '独立IP数', '独立客户端数', '平均会话时长(秒)', '人均对话句数', '总对话数'])
            
            for date_str, stats in sorted(self.daily_stats.items()):
                unique_ips = len(stats['unique_ips'])
                unique_clients = len(stats['unique_clients'])
                total_conversations = stats['total_conversations']
                avg_session_duration = sum(stats['session_durations']) / len(stats['session_durations']) if stats['session_durations'] else 0
                avg_conversations_per_user = total_conversations / unique_clients if unique_clients > 0 else 0
                
                writer.writerow([
                    date_str,
                    unique_ips,
                    unique_clients,
                    f"{avg_session_duration:.2f}",
                    f"{avg_conversations_per_user:.2f}",
                    total_conversations
                ])
        
        logger.info(f"每日统计汇总已保存到: {output_file}")
    
    def generate_client_sessions(self):
        """生成客户端会话详情"""
        output_file = self.output_directory / "client_sessions.csv"
        
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                '客户端ID', 'IP地址', '会话ID', '开始时间', '结束时间', '会话时长(秒)', '对话句数',
                '连接到Hello(秒)', 'Hello到Hi(秒)', 'Hi的TTS生成时间(秒)', 'Hi的TTS播放时间(秒)', '连接到首个PING间隔(秒)', '来源日志文件'
            ])
            
            for client_tag, session in self.sessions.items():
                if 'client_id' not in session:
                    continue
                
                # 获取会话信息
                client_id = session.get('client_id', '')
                ip = session.get('ip', '')
                session_id = session.get('session_id', session.get('extracted_session_id', ''))
                start_time = session.get('connection_open_time')
                end_time = session.get('disconnect_time')
                final_duration = session.get('final_duration', 0)
                conversation_count = len(session.get('conversations', []))
                
                # 获取日志来源信息
                log_sources = session.get('log_sources', {})
                source_file = session.get('file_name', 'unknown')
                
                # 输出调试信息
                logger.info(f"处理会话: {client_tag}")
                logger.info(f"  客户端ID: {client_id} (来源: {log_sources.get('client_id', '未知')})")
                logger.info(f"  IP地址: {ip} (来源: {log_sources.get('ip', '未知')})")
                logger.info(f"  会话ID: {session_id} (来源: {log_sources.get('session_id', '未知')})")
                logger.info(f"  开始时间: {start_time} (来源: {log_sources.get('connection_open', '未知')})")
                logger.info(f"  结束时间: {end_time} (来源: {log_sources.get('disconnect_time', '计算得出')})")
                logger.info(f"  会话时长: {final_duration:.2f}秒 (来源: {log_sources.get('duration', '计算得出')})")
                logger.info(f"  对话数: {conversation_count}")
                logger.info(f"  主要日志文件: {source_file}")
                
                # 获取相对路径
                source_file = session.get('file_name', 'unknown')
                relative_path = source_file if source_file != 'unknown' else ''
                
                writer.writerow([
                    client_id,
                    ip,
                    session_id,
                    start_time.strftime('%Y-%m-%d %H:%M:%S') if start_time else '',
                    end_time.strftime('%Y-%m-%d %H:%M:%S') if end_time else '',
                    f"{final_duration:.2f}",
                    conversation_count,
                    f"{session.get('connection_to_hello_delay', 0):.2f}",
                    f"{session.get('hello_to_hi_delay', 0):.2f}",
                    f"{session.get('hi_tts_generation_delay', 0):.2f}",
                    f"{session.get('hi_tts_playback_duration', 0):.2f}",
                    f"{session.get('connection_to_first_ping', 0):.2f}",
                    relative_path
                ])
        
        logger.info(f"客户端会话详情已保存到: {output_file}")
    
    def generate_conversation_latencies(self):
        """生成对话延迟明细"""
        output_file = self.output_directory / "conversation_latencies.csv"
        
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                '客户端ID', '会话ID', '用户对话序号', '用户输入', '用户输入时间',
                '语音识别时间(秒)', '审核时间(秒)', 'LLM首字符响应时间(秒)',
                'LLM句子序号', 'LLM输出句子', '首句延迟(秒)', 'TTS合成时间(秒)', 'TTS播放时间(秒)', '来源日志文件'
            ])
            
            for client_tag, session in self.sessions.items():
                if 'client_id' not in session:
                    continue
                
                client_id = session['client_id']
                session_id = session.get('session_id', '')
                
                conversations = session.get('conversations', [])
                for conv in conversations:
                    user_input = conv.get('user_input', '')
                    user_input_time = conv.get('start_time')
                    sequence = conv.get('sequence', 0)
                    
                    asr_duration = conv.get('asr_duration', 0)
                    policy_delay = conv.get('policy_check_delay', 0)
                    llm_first_token_delay = conv.get('llm_first_token_delay', 0)
                    first_sentence_delay = conv.get('first_sentence_delay', 0)
                    
                    # 获取日志来源
                    conv_log_sources = conv.get('log_sources', {})
                    source_file = conv_log_sources.get('file_name', session.get('file_name', 'unknown'))
                    
                    # 输出调试信息
                    logger.info(f"处理对话 {sequence} - 客户端: {client_id}")
                    logger.info(f"  用户输入时间: {user_input_time} (来源: {conv_log_sources.get('start_time', '未知')})")
                    logger.info(f"  用户输入: {user_input} (来源: {conv_log_sources.get('asr', '未知')})")
                    logger.info(f"  语音识别时间: {asr_duration}秒 (来源: {conv_log_sources.get('asr', '未知')})")
                    logger.info(f"  审核时间: {policy_delay}秒 (来源: {conv_log_sources.get('policy', '未知')})")
                    logger.info(f"  LLM首字符响应时间: {llm_first_token_delay}秒 (来源: {conv_log_sources.get('llm_first_token', '未知')})")
                    logger.info(f"  首句延迟: {first_sentence_delay}秒 (来源: {conv_log_sources.get('first_sentence', '未知')})")
                    
                    # 处理TTS生成
                    tts_generations = conv.get('tts_generations', [])
                    if not tts_generations:
                        # 没有TTS生成，写一行基础数据
                        writer.writerow([
                            client_id,
                            session_id,
                            sequence,
                            user_input,
                            user_input_time.strftime('%Y-%m-%d %H:%M:%S') if user_input_time else '',
                            f"{asr_duration:.2f}",
                            f"{policy_delay:.2f}",
                            f"{llm_first_token_delay:.2f}",
                            1,
                            conv.get('first_sentence', ''),
                            f"{first_sentence_delay:.2f}",
                            "0.00",
                            "0.00",
                            source_file
                        ])
                    else:
                        # 为每个TTS生成写一行
                        for i, tts_gen in enumerate(tts_generations):
                            sentence_number = tts_gen.get('sentence_number', i + 1)
                            tts_text = tts_gen.get('text', '')
                            generation_time = tts_gen.get('generation_time')
                            playback_time = tts_gen.get('playback_time')
                            
                            # 计算TTS合成时间（从开始处理到播放完成的时间）
                            tts_synthesis_delay = 0
                            if generation_time and playback_time:
                                tts_synthesis_delay = (playback_time - generation_time).total_seconds()
                            
                            # TTS播放时间
                            tts_playback_duration = tts_gen.get('playback_duration', 0)
                            
                            # 获取TTS相关的日志来源
                            tts_gen_source = tts_gen.get('log_source', '未知')
                            tts_playback_source = tts_gen.get('playback_log_source', '未知')
                            
                            # 输出TTS调试信息
                            if i == 0 or tts_text:  # 只为第一句或有重复文本的句子输出调试信息
                                logger.info(f"    TTS句子 {sentence_number}: {tts_text}")
                                logger.info(f"      TTS生成来源: {tts_gen_source}")
                                logger.info(f"      TTS播放来源: {tts_playback_source}")
                                logger.info(f"      TTS合成时间: {tts_synthesis_delay:.2f}秒")
                                logger.info(f"      TTS播放时间: {tts_playback_duration:.2f}秒")
                            
                            # 只在第一句显示语音识别、审核等时间
                            asr_time = asr_duration if i == 0 else 0
                            policy_time = policy_delay if i == 0 else 0
                            llm_time = llm_first_token_delay if i == 0 else 0
                            first_sent_delay = first_sentence_delay if i == 0 else 0
                            
                            writer.writerow([
                                client_id,
                                session_id,
                                sequence,
                                user_input if i == 0 else '',
                                user_input_time.strftime('%Y-%m-%d %H:%M:%S') if user_input_time and i == 0 else '',
                                f"{asr_time:.2f}",
                                f"{policy_time:.2f}",
                                f"{llm_time:.2f}",
                                sentence_number,
                                tts_text,
                                f"{first_sent_delay:.2f}",
                                f"{tts_synthesis_delay:.2f}",
                                f"{tts_playback_duration:.2f}",
                                source_file
                            ])
        
        logger.info(f"对话延迟明细已保存到: {output_file}")
    
    def generate_heartbeat_details(self):
        """生成心跳连接明细"""
        output_file = self.output_directory / "heartbeat_details.csv"
        
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                '客户端ID', '会话ID', 'PING序号', 'PING发送时间', 'PONG接收时间',
                'PING间隔(秒)', 'PONG响应时间(秒)', '连接状态评估'
            ])
            
            for client_tag, session in self.sessions.items():
                if 'client_id' not in session:
                    continue
                
                client_id = session['client_id']
                session_id = session.get('session_id', '')
                ping_times = session.get('ping_times', [])
                pong_times = session.get('pong_times', [])
                
                for i, ping_time in enumerate(ping_times):
                    ping_number = i + 1
                    pong_time = pong_times[i] if i < len(pong_times) else None
                    
                    # 计算PING间隔
                    ping_interval = 0
                    if i > 0:
                        ping_interval = (ping_time - ping_times[i-1]).total_seconds()
                    elif session.get('connection_open_time'):
                        ping_interval = (ping_time - session['connection_open_time']).total_seconds()
                    
                    # 计算PONG响应时间
                    pong_response_time = 0
                    if pong_time:
                        pong_response_time = (pong_time - ping_time).total_seconds()
                    
                    # 连接状态评估
                    status = "正常"
                    if pong_response_time > 3.0:
                        status = "响应慢"
                    elif not pong_time:
                        status = "无响应"
                    
                    writer.writerow([
                        client_id,
                        session_id,
                        ping_number,
                        ping_time.strftime('%Y-%m-%d %H:%M:%S'),
                        pong_time.strftime('%Y-%m-%d %H:%M:%S') if pong_time else '',
                        f"{ping_interval:.2f}",
                        f"{pong_response_time:.2f}",
                        status
                    ])
        
        logger.info(f"心跳连接明细已保存到: {output_file}")
    
    def generate_tts_parallel_analysis(self):
        """生成TTS并行处理分析"""
        output_file = self.output_directory / "tts_parallel_analysis.csv"
        
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                '客户端ID', '会话ID', '用户对话序号', 'TTS句子序号',
                'TTS开始时间', 'TTS完成时间', 'TTS耗时(秒)',
                '与前一句重叠时间(秒)', '时间节约(秒)'
            ])
            
            for client_tag, session in self.sessions.items():
                if 'client_id' not in session:
                    continue
                
                client_id = session['client_id']
                session_id = session.get('session_id', '')
                
                conversations = session.get('conversations', [])
                for conv in conversations:
                    sequence = conv.get('sequence', 0)
                    tts_generations = conv.get('tts_generations', [])
                    
                    prev_end_time = None
                    for i, tts_gen in enumerate(tts_generations):
                        sentence_number = tts_gen.get('sentence_number', i + 1)
                        generation_time = tts_gen.get('generation_time')
                        playback_time = tts_gen.get('playback_time')
                        
                        if not generation_time or not playback_time:
                            continue
                        
                        # TTS耗时
                        tts_duration = (playback_time - generation_time).total_seconds()
                        
                        # 与前一句的重叠时间
                        overlap_time = 0
                        time_saved = 0
                        
                        if prev_end_time and generation_time < prev_end_time:
                            overlap_time = (prev_end_time - generation_time).total_seconds()
                            time_saved = min(overlap_time, tts_duration)
                        
                        writer.writerow([
                            client_id,
                            session_id,
                            sequence,
                            sentence_number,
                            generation_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-4],
                            playback_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-4],
                            f"{tts_duration:.2f}",
                            f"{overlap_time:.2f}",
                            f"{time_saved:.2f}"
                        ])
                        
                        prev_end_time = playback_time
        
        logger.info(f"TTS并行处理分析已保存到: {output_file}")
    
    def analyze(self):
        """执行完整的日志分析"""
        logger.info("开始分析WebSocket日志...")
        
        # 查找所有日志文件
        log_files = []
        for root, dirs, files in os.walk(self.log_directory):
            for file in files:
                if file.endswith('.log'):
                    log_files.append(Path(root) / file)
        
        logger.info(f"找到 {len(log_files)} 个日志文件")
        
        # 解析每个日志文件
        for log_file in log_files:
            self.parse_log_file(log_file)
        
        # 计算指标
        logger.info("计算会话指标...")
        self.calculate_session_metrics()
        
        # 生成报告
        logger.info("生成CSV报告...")
        self.generate_csv_reports()
        
        logger.info(f"分析完成！报告已保存到: {self.output_directory}")
        logger.info(f"共分析了 {len(self.sessions)} 个会话")

def main():
    """主函数"""
    log_directory = "/Users/<USER>/Downloads/log"
    
    if not os.path.exists(log_directory):
        logger.error(f"日志目录不存在: {log_directory}")
        return
    
    analyzer = LogAnalyzer(log_directory)
    analyzer.analyze()

if __name__ == "__main__":
    main()