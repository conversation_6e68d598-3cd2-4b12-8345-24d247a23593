from plugins_func.register import register_function, ToolType, ActionResponse, Action
from config.logger import setup_logging
from config.role_loader import get_role_loader
from core.utils.voice_mapper import get_voice_mapper
import asyncio

TAG = __name__
logger = setup_logging()

# 获取角色加载器实例
role_loader = get_role_loader()
# 获取声音映射器实例
voice_mapper = get_voice_mapper()

def get_next_role_in_order(current_role):
    """获取配置文件中的下一个角色，支持循环"""
    try:
        available_roles = role_loader.get_available_roles()
        if not available_roles or len(available_roles) == 1:
            return current_role
        
        if current_role not in available_roles:
            # 如果当前角色不在配置中，返回第一个角色
            return available_roles[0]
        
        current_index = available_roles.index(current_role)
        next_index = (current_index + 1) % len(available_roles)
        return available_roles[next_index]
    except Exception as e:
        logger.bind(tag=TAG).error(f"获取下一个角色失败: {e}")
        return current_role

def get_switch_role_function_desc():
    """动态生成function描述，包含当前可用的角色列表"""
    available_roles = role_loader.get_available_roles()
    roles_str = ",".join(available_roles)
    
    return {
        "type": "function",
        "function": {
            "name": "switch_role",
            "description": f"仅当用户明确要求切换角色时调用。必须包含明确的角色切换关键词，如'切换到XX'、'换个角色'、'切换角色'、'更换角色'、'变成XX角色'等。不要在用户只是在正常对话、谈论其他话题或语音识别不清楚时调用此函数。\n可用角色：[{roles_str}]。",
            "parameters": {
                "type": "object",
                "properties": {
                    "role_personality": {
                        "type": "string",
                        "description": "要切换的角色名称（可选）。当用户指定具体角色名称或别名时传入此参数，如果用户只说'换个角色'等不指定具体角色，可不传此参数。"
                    }
                },
                "required": []
            }
        }
    }

# 注册函数时使用动态生成的描述
switch_role_function_desc = get_switch_role_function_desc()

@register_function('switch_role', switch_role_function_desc, ToolType.CHANGE_SYS_PROMPT)
def switch_role(conn, role_personality: str = None):
    """
    切换助手角色
    
    Args:
        conn: 连接对象
        role_personality: 角色性格类型（可选，如果为None则自动切换到下一个角色）
    """
    try:
        # 记录是否是自动切换
        is_auto_switch = role_personality is None
        
        # 获取当前状态
        old_role = getattr(conn, 'current_role', None)
        if hasattr(conn, 'user_profile_manager') and conn.user_profile_manager:
            old_role = conn.user_profile_manager.current_role
        assistant_name = getattr(conn, 'assistant_name', '语嫣')
        
        # 如果没有指定角色，自动切换到下一个角色
        if role_personality is None:
            if not old_role:
                # 如果没有当前角色，使用默认角色
                available_roles = role_loader.get_available_roles()
                role_personality = available_roles[0] if available_roles else None
            else:
                role_personality = get_next_role_in_order(old_role)
            
            if not role_personality:
                return ActionResponse(action=Action.RESPONSE, result="切换角色失败", response="没有可用的角色配置")
            
            logger.bind(tag=TAG).info(f"自动切换角色: {old_role} -> {role_personality}")
        
        # 标准化角色名称（支持别名）
        normalized_role = role_loader.normalize_role_name(role_personality)
        
        if not normalized_role:
            available_roles = role_loader.get_available_roles()
            error_msg = f"不支持的角色性格。可选择的角色性格有：{', '.join(available_roles)}"
            logger.bind(tag=TAG).warning(f"角色切换失败: {error_msg}")
            return ActionResponse(action=Action.RESPONSE, result="切换角色失败", response=error_msg)
        
        # 检查是否真的需要切换
        if normalized_role == old_role:
            return ActionResponse(action=Action.RESPONSE, result="角色未变化", response="我本来就是这个角色呀！")
        
        # 获取角色配置
        role_config = role_loader.get_role_config(normalized_role)
        if not role_config:
            error_msg = f"获取角色配置失败: {normalized_role}"
            logger.bind(tag=TAG).error(error_msg)
            return ActionResponse(action=Action.RESPONSE, result="切换角色失败", response="角色配置加载失败")
        
        # 使用用户配置管理器切换角色
        if hasattr(conn, 'user_profile_manager') and conn.user_profile_manager:
            # 更新角色
            old_role_name = conn.user_profile_manager.current_role
            conn.user_profile_manager.current_role = normalized_role
            
            # 获取角色的默认声音
            default_voice = voice_mapper.get_default_voice_for_role(normalized_role)
            if default_voice:
                conn.user_profile_manager.set_voice_config(
                    default_voice["name"],
                    default_voice["voice_id"],
                    default_voice["engine"]
                )
                logger.bind(tag=TAG).info(f"[角色切换] 设置默认声音: {default_voice['name']}")
            
            # 应用角色配置
            if hasattr(conn.user_profile_manager, 'apply_role_config'):
                try:
                    # 检查是否有运行的事件循环
                    try:
                        loop = asyncio.get_running_loop()
                        # 有事件循环，可以安全地创建任务
                        asyncio.create_task(conn.user_profile_manager.apply_role_config())
                        logger.bind(tag=TAG).debug("[角色切换] 通过异步任务应用角色配置")
                    except RuntimeError:
                        # 没有运行的事件循环，跳过异步操作
                        logger.bind(tag=TAG).warning("[角色切换] 没有运行的事件循环，跳过异步角色配置应用")
                except Exception as e:
                    logger.bind(tag=TAG).error(f"[角色切换] 应用角色配置失败: {e}")
            
            # 保存设置到Redis
            conn.user_profile_manager.save_user_settings()
            
            logger.bind(tag=TAG).info(f"[角色切换] 通过UserProfileManager完成: {old_role_name} -> {normalized_role}")
        else:
            # 兼容性处理：如果没有用户配置管理器，使用旧方法
            conn.current_role = normalized_role
            # 保存用户设置到Redis
            if hasattr(conn, '_save_user_settings'):
                conn._save_user_settings()
            
            # 应用角色配置
            if hasattr(conn, '_apply_role_config'):
                conn._apply_role_config()
            
            # 更新系统提示词
            new_prompt = role_config["prompt"].replace("{{assistant_name}}", assistant_name)
            if hasattr(conn, 'change_system_prompt'):
                conn.change_system_prompt(new_prompt)
            
            logger.bind(tag=TAG).warning(f"[角色切换] 使用兼容模式完成角色切换")
        
        # 生成个性化回复
        response = _generate_switch_response(normalized_role, assistant_name, is_auto_switch)
        
        return ActionResponse(action=Action.RESPONSE, result="切换角色成功", response=response)
        
    except Exception as e:
        logger.bind(tag=TAG).error(f"切换角色失败: {e}")
        return ActionResponse(action=Action.RESPONSE, result="切换角色失败", response="抱歉，切换角色时出现了问题")

def _generate_switch_response(normalized_role, assistant_name, is_auto_switch=False):
    """生成角色切换的个性化回复"""
    return f"好的，我现在是{normalized_role}性格的{assistant_name}了！"

# 定义声音切换函数描述
def get_switch_voice_function_desc():
    """动态生成声音切换function描述"""
    return {
        "type": "function",
        "function": {
            "name": "switch_voice",
            "description": "仅当用户明确要求切换声音时调用。必须包含明确的声音切换关键词，如'换个声音'、'换一个声音'、'试试其他声音'、'切换声音'、'更换声音'等。不要在用户只是在正常对话、谈论其他话题或语音识别不清楚时调用此函数。",
            "parameters": {
                "type": "object", 
                "properties": {
                    "voice_name": {
                        "type": "string",
                        "description": "要切换的声音名称（可选）。如果不提供，将自动切换到下一个可用声音。"
                    }
                },
                "required": []
            }
        }
    }

# 注册声音切换函数
switch_voice_function_desc = get_switch_voice_function_desc()

@register_function('switch_voice', switch_voice_function_desc, ToolType.CHANGE_SYS_PROMPT)
def switch_voice(conn, voice_name: str = None):
    """
    切换当前角色的声音
    
    Args:
        conn: 连接对象
        voice_name: 声音名称（可选）
    """
    try:
        # 检查是否有用户配置管理器
        if not hasattr(conn, 'user_profile_manager') or not conn.user_profile_manager:
            logger.bind(tag=TAG).error("用户配置管理器不可用")
            return ActionResponse(action=Action.RESPONSE, result="切换声音失败", response="系统配置错误，无法切换声音")
        
        user_manager = conn.user_profile_manager
        current_role = user_manager.current_role
        
        if not current_role:
            return ActionResponse(action=Action.RESPONSE, result="切换声音失败", response="请先选择一个角色")
        
        # 获取当前角色的可用声音
        available_voices = user_manager.get_available_voices_for_role(current_role)
        if not available_voices:
            return ActionResponse(action=Action.RESPONSE, result="切换声音失败", response="当前角色没有可用的声音选项")
        
        old_voice_name = user_manager.current_voice_name
        success = False
        
        if voice_name:
            # 切换到指定声音
            success = user_manager.switch_to_voice_by_name(voice_name)
            if not success:
                # 如果指定声音不存在，提示可用声音
                voice_names = [v["name"] for v in available_voices]
                return ActionResponse(
                    action=Action.RESPONSE, 
                    result="切换声音失败", 
                    response=f"没找到声音'{voice_name}'。可选声音有：{', '.join(voice_names)}"
                )
        else:
            # 切换到下一个声音
            success = user_manager.switch_to_next_voice()
        
        if success:
            new_voice_name = user_manager.current_voice_name
            logger.bind(tag=TAG).info(f"声音切换成功: {old_voice_name} -> {new_voice_name}")
            
            # 生成个性化回复
            if len(available_voices) > 1:
                response = f"好的，已经换成{new_voice_name}的声音了！感觉怎么样？"
            else:
                response = f"这是{new_voice_name}的声音哦！"
            
            return ActionResponse(action=Action.RESPONSE, result="切换声音成功", response=response)
        else:
            return ActionResponse(action=Action.RESPONSE, result="切换声音失败", response="抱歉，切换声音时出现了问题")
        
    except Exception as e:
        logger.bind(tag=TAG).error(f"切换声音失败: {e}")
        return ActionResponse(action=Action.RESPONSE, result="切换声音失败", response="抱歉，切换声音时出现了问题")

def reload_role_config():
    """重新加载角色配置（用于热更新）"""
    global switch_role_function_desc, switch_voice_function_desc
    role_loader.reload_config()
    voice_mapper.reload_mappings()
    switch_role_function_desc = get_switch_role_function_desc()
    switch_voice_function_desc = get_switch_voice_function_desc()
    logger.bind(tag=TAG).info("角色配置和声音映射已重新加载")
