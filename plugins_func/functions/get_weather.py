import requests
from bs4 import BeautifulSoup
from config.logger import setup_logging
from plugins_func.register import register_function, ToolType, ActionResponse, Action
from core.utils.util import get_ip_info

TAG = __name__
logger = setup_logging()

GET_WEATHER_FUNCTION_DESC = {
    "type": "function",
    "function": {
        "name": "get_weather",
        "description": (
            "获取某个地点的天气。\n如果用户提供了具体地点，如杭州天气，则使用该地点。"
            "如果用户说的是省份，默认用省会城市。如果用户说的不是省份或城市而是一个地名，默认用该地所在省份的省会城市。"
            "如果用户没有指明地点，说“天气怎么样”，“今天天气如何”，“今天会下雨吗”，“今天会不会很热”，“未来几天会下雪吗”之类，则不要传递location参数，或者将location设置为空字符串。"
        ),
        "parameters": {
            "type": "object",
            "properties": {
                "location": {
                    "type": "string",
                    "description": "地点名，例如杭州。这是一个可选参数。如果用户未指定地点，则完全省略此参数，或者设置为空字符串。",
                },
                "lang": {
                    "type": "string",
                    "description": "返回用户使用的语言code，例如zh_CN/zh_HK/en_US/ja_JP等，默认zh_CN",
                },
            },
            "required": ["lang"],
        },
    },
}

HEADERS = {
    "User-Agent": (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36"
    )
}

# 天气代码 https://dev.qweather.com/docs/resource/icons/#weather-icons
WEATHER_CODE_MAP = {
    "100": "晴",
    "101": "多云",
    "102": "少云",
    "103": "晴间多云",
    "104": "阴",
    "150": "晴",
    "151": "多云",
    "152": "少云",
    "153": "晴间多云",
    "300": "阵雨",
    "301": "强阵雨",
    "302": "雷阵雨",
    "303": "强雷阵雨",
    "304": "雷阵雨伴有冰雹",
    "305": "小雨",
    "306": "中雨",
    "307": "大雨",
    "308": "极端降雨",
    "309": "毛毛雨/细雨",
    "310": "暴雨",
    "311": "大暴雨",
    "312": "特大暴雨",
    "313": "冻雨",
    "314": "小到中雨",
    "315": "中到大雨",
    "316": "大到暴雨",
    "317": "暴雨到大暴雨",
    "318": "大暴雨到特大暴雨",
    "350": "阵雨",
    "351": "强阵雨",
    "399": "雨",
    "400": "小雪",
    "401": "中雪",
    "402": "大雪",
    "403": "暴雪",
    "404": "雨夹雪",
    "405": "雨雪天气",
    "406": "阵雨夹雪",
    "407": "阵雪",
    "408": "小到中雪",
    "409": "中到大雪",
    "410": "大到暴雪",
    "456": "阵雨夹雪",
    "457": "阵雪",
    "499": "雪",
    "500": "薄雾",
    "501": "雾",
    "502": "霾",
    "503": "扬沙",
    "504": "浮尘",
    "507": "沙尘暴",
    "508": "强沙尘暴",
    "509": "浓雾",
    "510": "强浓雾",
    "511": "中度霾",
    "512": "重度霾",
    "513": "严重霾",
    "514": "大雾",
    "515": "特强浓雾",
    "900": "热",
    "901": "冷",
    "999": "未知",
}


def fetch_city_info(location, api_key):
    url = f"https://geoapi.qweather.com/v2/city/lookup?key={api_key}&location={location}&lang=zh"
    try:
        # 连接超时5秒，数据读取超时15秒
        response = requests.get(url, headers=HEADERS, timeout=(5, 15))
        response.raise_for_status()
        data = response.json()
        return data.get("location", [])[0] if data.get("location") else None
    except requests.exceptions.Timeout:
        logger.bind(tag=TAG).error(f"城市查询API超时: {url}")
        return None
    except requests.exceptions.RequestException as e:
        logger.bind(tag=TAG).error(f"城市查询API请求失败: {e}")
        return None


def fetch_weather_page(url):
    try:
        # 连接超时5秒，数据读取超时15秒
        response = requests.get(url, headers=HEADERS, timeout=(5, 15))
        response.raise_for_status()
        return BeautifulSoup(response.text, "html.parser")
    except requests.exceptions.Timeout:
        logger.bind(tag=TAG).error(f"天气页面获取超时: {url}")
        return None
    except requests.exceptions.RequestException as e:
        logger.bind(tag=TAG).error(f"天气页面获取失败: {e}")
        return None


def parse_weather_info(soup):
    city_name = soup.select_one("h1.c-submenu__location").get_text(strip=True)

    current_abstract = soup.select_one(".c-city-weather-current .current-abstract")
    current_abstract = (
        current_abstract.get_text(strip=True) if current_abstract else "未知"
    )

    current_basic = {}
    for item in soup.select(
        ".c-city-weather-current .current-basic .current-basic___item"
    ):
        parts = item.get_text(strip=True, separator=" ").split(" ")
        if len(parts) == 2:
            key, value = parts[1], parts[0]
            current_basic[key] = value

    temps_list = []
    for row in soup.select(".city-forecast-tabs__row")[:7]:  # 取前7天的数据
        date = row.select_one(".date-bg .date").get_text(strip=True)
        weather_code = (
            row.select_one(".date-bg .icon")["src"].split("/")[-1].split(".")[0]
        )
        weather = WEATHER_CODE_MAP.get(weather_code, "未知")
        temps = [span.get_text(strip=True) for span in row.select(".tmp-cont .temp")]
        high_temp, low_temp = (temps[0], temps[-1]) if len(temps) >= 2 else (None, None)
        temps_list.append((date, weather, high_temp, low_temp))

    return city_name, current_abstract, current_basic, temps_list


@register_function("get_weather", GET_WEATHER_FUNCTION_DESC, ToolType.SYSTEM_CTL)
def get_weather(conn, location: str = None, lang: str = "zh_CN"):
    # 参数验证和清理
    if location is not None and not isinstance(location, str):
        logger.bind(tag=TAG).warning(f"无效的location参数类型: {type(location)}，转换为字符串")
        try:
            location = str(location)
        except:
            location = None
    
    # 如果 location 是空字符串，将其设置为 None
    if location == "":
        location = None
        
    # 确保 lang 是有效的字符串
    if not isinstance(lang, str) or not lang:
        logger.bind(tag=TAG).warning(f"无效的lang参数: {lang}，使用默认值 zh_CN")
        lang = "zh_CN"
        
    # 获取配置
    api_key = conn.config["plugins"]["get_weather"]["api_key"]
    default_location = conn.config["plugins"]["get_weather"]["default_location"]
    client_ip = conn.client_ip
    
    # 日志记录原始参数
    logger.bind(tag=TAG).debug(f"天气查询原始参数: location={location}, lang={lang}")
    
    # 优先使用用户提供的location参数
    if not location:
        # 通过客户端IP解析城市
        if client_ip:
            # 动态解析IP对应的城市信息
            ip_info = get_ip_info(client_ip, logger)
            location = ip_info.get("city") if ip_info and "city" in ip_info else None
        else:
            # 若IP解析失败或无IP，使用默认位置
            location = default_location
            
    # 最终使用的位置信息日志
    logger.bind(tag=TAG).info(f"天气查询最终位置: {location}")

    # 检查 location 是否为空
    if not location or location.strip() == "":
        return ActionResponse(
            Action.REQLLM, "哎呀，我还不知道你在哪个城市呢", None
        )

    # 添加重试机制 - 城市信息查询
    city_info = None
    for attempt in range(2):  # 重试1次，总共2次尝试
        logger.bind(tag=TAG).debug(f"城市信息查询尝试 {attempt + 1}/2")
        city_info = fetch_city_info(location, api_key)
        if city_info:
            break
        if attempt == 0:  # 第一次失败时记录警告
            logger.bind(tag=TAG).warning(f"城市信息查询第1次失败，正在重试...")
    
    if not city_info:
        logger.bind(tag=TAG).error(f"城市信息查询失败，已重试1次: {location}")
        return ActionResponse(
            Action.REQLLM, "不好意思，我暂时查不了天气，晚点再试试", None
        )

    # 添加重试机制 - 天气页面获取
    soup = None
    for attempt in range(2):  # 重试1次，总共2次尝试
        logger.bind(tag=TAG).debug(f"天气页面获取尝试 {attempt + 1}/2")
        soup = fetch_weather_page(city_info["fxLink"])
        if soup:
            break
        if attempt == 0:  # 第一次失败时记录警告
            logger.bind(tag=TAG).warning(f"天气页面获取第1次失败，正在重试...")
    
    if not soup:
        logger.bind(tag=TAG).error(f"天气页面获取失败，已重试1次: {city_info['fxLink']}")
        return ActionResponse(
            Action.REQLLM, "不好意思，我暂时查不了天气，晚点再试试", None
        )
    
    try:
        city_name, current_abstract, current_basic, temps_list = parse_weather_info(soup)
    except Exception as e:
        logger.bind(tag=TAG).error(f"天气信息解析失败: {e}")
        return ActionResponse(
            Action.REQLLM, "不好意思，我暂时查不了天气，晚点再试试", None
        )

    weather_report = f"您查询的位置是：{city_name}\n\n当前天气: {current_abstract}\n"

    # 添加有效的当前天气参数
    if current_basic:
        weather_report += "详细参数：\n"
        for key, value in current_basic.items():
            if value != "0":  # 过滤无效值
                weather_report += f"  · {key}: {value}\n"

    # 添加7天预报
    weather_report += "\n未来7天预报：\n"
    for date, weather, high, low in temps_list:
        weather_report += f"{date}: {weather}，气温 {low}~{high}\n"

    # 提示语
    weather_report += "\n（如需某一天的具体天气，请告诉我日期）"

    return ActionResponse(Action.REQLLM, weather_report, None)
