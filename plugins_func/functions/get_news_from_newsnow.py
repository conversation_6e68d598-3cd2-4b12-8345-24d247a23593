import random
import requests
import json
from config.logger import setup_logging
from plugins_func.register import register_function, ToolType, ActionResponse, Action
from markitdown import MarkItDown

TAG = __name__
logger = setup_logging()

CHANNEL_MAP = {
    "V2EX": "v2ex-share",
    "知乎": "zhihu",
    "微博": "weibo",
    "联合早报": "zaobao",
    "酷安": "coolapk",
    "MKTNews": "mktnews-flash",
    "华尔街见闻": "wallstreetcn-quick",
    "36氪": "36kr-quick",
    "抖音": "douyin",
    "虎扑": "hupu",
    "百度贴吧": "tieba",
    "今日头条": "toutiao",
    "IT之家": "ithome",
    "澎湃新闻": "thepaper",
    "卫星通讯社": "sputniknewscn",
    "参考消息": "cankaoxiaoxi",
    "远景论坛": "pcbeta-windows11",
    "财联社": "cls-depth",
    "雪球": "xueqiu-hotstock",
    "格隆汇": "gelonghui",
    "法布财经": "fastbull-express",
    "Solidot": "solidot",
    "Hacker News": "hackernews",
    "Product Hunt": "producthunt",
    "Github": "github-trending-today",
    # "哔哩哔哩": "bilibili-hot-search",
    # "快手": "kuaishou",
    "靠谱新闻": "kaopu",
    "金十数据": "jin10",
    "百度热搜": "baidu",
    "牛客": "nowcoder",
    "少数派": "sspai",
    "稀土掘金": "juejin",
    "凤凰网": "ifeng",
    "虫部落": "chongbuluo-latest",
}


# 新闻类别映射，根据类别返回相关新闻源
CATEGORY_MAP = {
    "财经": ["财联社", "雪球", "格隆汇", "华尔街见闻", "36氪", "金十数据"],
    "娱乐": ["抖音", "微博", "快手"],
    "科技": ["IT之家", "Solidot", "Github", "稀土掘金", "少数派"],
    "综合": ["澎湃新闻", "今日头条", "凤凰网", "参考消息"],
    "热搜": ["百度热搜", "微博", "抖音"]
}

# 特定平台映射
PLATFORM_MAP = {
    "微博热搜": "微博",
    "抖音热门话题": "抖音",
    "微博": "微博",
    "抖音": "抖音"
}


GET_NEWS_FROM_NEWSNOW_FUNCTION_DESC = {
    "type": "function",
    "function": {
        "name": "get_news_from_newsnow",
        "description": (
            "获取最新新闻内容。仅在用户明确要求查看新闻时触发。\n"
            "支持两种查询模式：\n"
            "1. 新闻类别查询：财经新闻、娱乐新闻、科技新闻、综合新闻、热搜话题\n"
            "2. 特定平台查询：微博热搜、抖音热门话题\n"
            "用户可以要求获取详细内容来查看完整新闻。"
        ),
        "parameters": {
            "type": "object",
            "properties": {
                "category": {
                    "type": "string",
                    "description": "用户的新闻查询请求。可以是新闻类别（如'财经新闻'、'娱乐新闻'、'科技新闻'、'综合新闻'、'热搜话题'）或特定平台（如'微博热搜'、'抖音热门话题'）。默认为综合新闻。",
                },
                "detail": {
                    "type": "boolean",
                    "description": "是否获取详细内容，默认为false。如果为true，则获取上一条新闻的详细内容",
                },
                "lang": {
                    "type": "string",
                    "description": "返回用户使用的语言code，例如zh_CN/zh_HK/en_US/ja_JP等，默认zh_CN",
                },
            },
            "required": ["lang"],
        },
    },
}


def fetch_news_from_api(conn, source="thepaper"):
    """从API获取新闻列表"""
    try:
        api_url = f"https://newsnow.busiyi.world/api/s?id={source}"
        if conn.config["plugins"].get("get_news_from_newsnow") and conn.config[
            "plugins"
        ]["get_news_from_newsnow"].get("url"):
            api_url = conn.config["plugins"]["get_news_from_newsnow"]["url"] + source

        logger.bind(tag=TAG).debug(f"请求新闻API: {api_url}")
        
        response = requests.get(api_url, timeout=10)
        response.raise_for_status()

        data = response.json()
        
        logger.bind(tag=TAG).debug(f"API响应状态: {response.status_code}")
        logger.bind(tag=TAG).debug(f"API响应数据键: {list(data.keys()) if isinstance(data, dict) else 'non-dict response'}")

        if "items" in data:
            items = data["items"]
            logger.bind(tag=TAG).info(f"API返回 {len(items)} 条新闻项目")
            
            # 显示前3条新闻的标题（用于调试）
            for i, item in enumerate(items[:3]):
                logger.bind(tag=TAG).debug(f"新闻 {i+1}: {item.get('title', '无标题')}")
                
            return items
        else:
            logger.bind(tag=TAG).error(f"获取新闻API响应格式错误: {data}")
            return []

    except Exception as e:
        logger.bind(tag=TAG).error(f"获取新闻API失败: {e}")
        return []


def fetch_news_detail(url):
    """获取新闻详情页内容并使用MarkItDown清理HTML"""
    try:
        response = requests.get(url, timeout=5)  # 减少超时时间到5秒
        response.raise_for_status()

        # 使用MarkItDown清理HTML内容
        md = MarkItDown(enable_plugins=False)
        result = md.convert(response)

        # 获取清理后的文本内容
        clean_text = result.text_content

        # 如果清理后的内容为空，返回提示信息
        if not clean_text or len(clean_text.strip()) == 0:
            logger.bind(tag=TAG).warning(f"清理后的新闻内容为空: {url}")
            return "无法解析新闻详情内容，可能是网站结构特殊或内容受限。"

        return clean_text
    except requests.exceptions.RequestException as e:
        # 详细记录不同类型的访问失败
        if hasattr(e, 'response') and e.response is not None:
            status_code = e.response.status_code
            logger.bind(tag=TAG).warning(f"URL访问失败 {status_code}: {url}, 错误: {e}")
            if status_code in [403, 444, 429, 503]:
                logger.bind(tag=TAG).info(f"检测到反爬虫机制，状态码: {status_code}")
        else:
            logger.bind(tag=TAG).warning(f"URL访问失败: {url}, 错误: {e}")
        return "URL_ACCESS_FAILED"
    except Exception as e:
        logger.bind(tag=TAG).error(f"获取新闻详情失败: {e}")
        return "无法获取详细内容"


@register_function(
    "get_news_from_newsnow",
    GET_NEWS_FROM_NEWSNOW_FUNCTION_DESC,
    ToolType.SYSTEM_CTL,
)
def get_news_from_newsnow(
    conn, category: str = "综合新闻", detail: bool = False, lang: str = "zh_CN"
):
    """获取新闻并随机选择一条进行播报，或获取上一条新闻的详细内容"""
    try:
        # 如果detail为True，获取上一条新闻的详细内容
        detail = str(detail).lower() == "true"
        if detail:
            if (
                not hasattr(conn, "last_newsnow_link")
                or not conn.last_newsnow_link
                or "url" not in conn.last_newsnow_link
            ):
                return ActionResponse(
                    Action.REQLLM,
                    "抱歉，没有找到最近查询的新闻，请先获取一条新闻。",
                    None,
                )

            url = conn.last_newsnow_link.get("url")
            title = conn.last_newsnow_link.get("title", "未知标题")
            source_id = conn.last_newsnow_link.get("source_id", "thepaper")
            source_name = CHANNEL_MAP.get(source_id, "未知来源")

            if not url or url == "#":
                return ActionResponse(
                    Action.REQLLM, "抱歉，该新闻没有可用的链接获取详细内容。", None
                )

            logger.bind(tag=TAG).debug(
                f"获取新闻详情: {title}, 来源: {source_name}, URL={url}"
            )

            # 获取新闻详情
            detail_content = fetch_news_detail(url)

            # 如果URL访问失败，自动切换到搜索模式
            if detail_content == "URL_ACCESS_FAILED":
                logger.bind(tag=TAG).info(f"URL访问失败，切换到搜索模式: {title}")
                try:
                    # 导入搜索函数
                    from plugins_func.functions.search_web import search_web
                    
                    # 使用新闻标题进行搜索
                    search_query = f"{title} 详细内容"
                    search_result = search_web(conn, search_query)
                    
                    if search_result.action == Action.REQLLM and search_result.result:
                        # 构建包含搜索结果的详情报告
                        detail_report = (
                            f"根据下列数据，用{lang}回应用户的新闻详情查询请求：\n\n"
                            f"新闻标题: {title}\n"
                            f"搜索到的相关信息: {search_result.result}\n\n"
                            f"(请对上述信息进行整理，以自然、流畅的方式向用户播报，"
                            f"可以提及这是通过搜索获取的相关信息)"
                        )
                        return ActionResponse(Action.REQLLM, detail_report, None)
                    else:
                        # 搜索也失败的情况
                        return ActionResponse(
                            Action.REQLLM,
                            f"抱歉，《{title}》的详细内容暂时无法获取，可能是网站访问受限。",
                            None,
                        )
                except Exception as search_error:
                    logger.bind(tag=TAG).error(f"搜索降级失败: {search_error}")
                    return ActionResponse(
                        Action.REQLLM,
                        f"抱歉，无法获取《{title}》的详细内容，请稍后再试。",
                        None,
                    )
            
            # 其他类型的获取失败
            elif not detail_content or detail_content == "无法获取详细内容":
                return ActionResponse(
                    Action.REQLLM,
                    f"抱歉，无法获取《{title}》的详细内容，可能是链接已失效或网站结构发生变化。",
                    None,
                )

            # 构建详情报告
            detail_report = (
                f"根据下列数据，用{lang}回应用户的新闻详情查询请求：\n\n"
                f"新闻标题: {title}\n"
                # f"新闻来源: {source_name}\n"
                f"详细内容: {detail_content}\n\n"
                f"(请对上述新闻内容进行总结，提取关键信息，以自然、流畅的方式向用户播报，"
                f"不要提及这是总结，就像是在讲述一个完整的新闻故事)"
            )

            return ActionResponse(Action.REQLLM, detail_report, None)

        # 否则，获取新闻列表并随机选择一条
        # 解析查询类型：类别查询 vs 特定平台查询
        selected_source = None
        source_name = None
        
        # 检查是否是特定平台查询
        if category in PLATFORM_MAP:
            selected_source = PLATFORM_MAP[category]
            source_name = selected_source
            logger.bind(tag=TAG).info(f"特定平台查询: {category} -> {selected_source}")
        
        # 检查是否是类别查询，支持模糊匹配
        else:
            # 首先尝试直接匹配
            if category in CATEGORY_MAP:
                available_sources = CATEGORY_MAP[category]
                selected_source = random.choice(available_sources)
                source_name = selected_source
                logger.bind(tag=TAG).info(f"类别查询: {category} -> 选择源: {selected_source}")
            else:
                # 尝试模糊匹配（去掉"新闻"后缀）
                category_key = None
                for key in CATEGORY_MAP.keys():
                    if category.startswith(key) or key in category:
                        category_key = key
                        break
                
                if category_key:
                    available_sources = CATEGORY_MAP[category_key]
                    selected_source = random.choice(available_sources)
                    source_name = selected_source
                    logger.bind(tag=TAG).info(f"模糊匹配类别查询: {category} -> {category_key} -> 选择源: {selected_source}")
                else:
                    # 如果都不匹配，使用默认的综合新闻
                    available_sources = CATEGORY_MAP["综合"]
                    selected_source = random.choice(available_sources)
                    source_name = selected_source
                    logger.bind(tag=TAG).warning(f"未识别的查询: {category}，使用综合新闻 -> {selected_source}")

        # 获取对应的英文ID
        english_source_id = CHANNEL_MAP.get(selected_source)
        if not english_source_id:
            logger.bind(tag=TAG).error(f"无法找到新闻源映射: {selected_source}，使用默认源")
            english_source_id = "thepaper"
            source_name = "澎湃新闻"

        logger.bind(tag=TAG).info(f"获取新闻: 查询={category}, 新闻源={source_name}({english_source_id})")

        # 获取新闻列表
        news_items = fetch_news_from_api(conn, english_source_id)

        if not news_items:
            return ActionResponse(
                Action.REQLLM,
                f"抱歉，未能从{source_name}获取到新闻信息，请稍后再试或尝试其他新闻源。",
                None,
            )

        logger.bind(tag=TAG).info(f"成功获取到 {len(news_items)} 条新闻")
        
        # 构建新闻标题列表，限制在4000字符内
        news_titles = []
        total_length = 0
        max_length = 4000
        truncated = False
        
        for i, news_item in enumerate(news_items):
            title = news_item.get('title', '未知标题')
            # 格式：- 标题
            formatted_title = f"- {title}\n"
            
            # 检查是否超过长度限制
            if total_length + len(formatted_title) > max_length:
                truncated = True
                break
            
            news_titles.append(formatted_title)
            total_length += len(formatted_title)
        
        # 构建最终的新闻列表字符串
        news_list_str = "".join(news_titles)
        
        # 如果发生截断，添加提示
        if truncated:
            truncate_info = f"...（共{len(news_items)}条新闻，已截断显示前{len(news_titles)}条）"
            if total_length + len(truncate_info) <= max_length:
                news_list_str += truncate_info
        
        # 保存第一条新闻链接到连接对象，以便后续查询详情
        if news_items:
            first_news = news_items[0]
            if not hasattr(conn, "last_newsnow_link"):
                conn.last_newsnow_link = {}
            conn.last_newsnow_link = {
                "url": first_news.get("url", "#"),
                "title": first_news.get("title", "未知标题"),
                "source_id": english_source_id,
            }
        
        # 记录返回的新闻数量
        logger.bind(tag=TAG).info(f"返回 {len(news_titles)} 条新闻标题{'（已截断）' if truncated else ''}")
        logger.bind(tag=TAG).debug(f"新闻列表字符串长度: {len(news_list_str)}")

        # 构建新闻报告
        news_report = (
            f"新闻列表:\n{news_list_str}\n"
            f"(请以自然、流畅的方式向用户播报这些新闻标题，"
            f"可以选择其中几条重点介绍)"
        )

        return ActionResponse(Action.REQLLM, news_report, None)

    except Exception as e:
        logger.bind(tag=TAG).error(f"获取新闻出错: {e}")
        logger.bind(tag=TAG).error(f"异常类型: {type(e).__name__}")
        logger.bind(tag=TAG).error(f"查询参数: category={category}, detail={detail}, lang={lang}")
        import traceback
        logger.bind(tag=TAG).error(f"异常堆栈: {traceback.format_exc()}")
        return ActionResponse(
            Action.REQLLM, "抱歉，获取新闻时发生错误，请稍后再试。", None
        )