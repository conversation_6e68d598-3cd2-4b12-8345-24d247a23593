import os
import asyncio
from google import genai
from google.genai import types
from google.genai.types import Tool, GenerateContentConfig
from config.logger import setup_logging
from plugins_func.register import register_function, ToolType, ActionResponse, Action

TAG = __name__
logger = setup_logging()

search_web_function_desc = {
    "type": "function",
    "function": {
        "name": "search_web",
        "description": (
            "搜索互联网内容。当遇到时效性强的问题、用户需要实时更新的内容时，调用本函数。"
        ),
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "用户的原始问题或需求",
                },
            },
            "required": ["query"],
        },
    },
}

class GeminiSearchClient:
    def __init__(self, api_key: str = None, model: str = "gemini-2.5-flash"):
        self.api_key = api_key
        self.model = model
        
        # Check if GOOGLE_GENAI_USE_VERTEXAI environment variable is set to True
        use_vertex_ai = os.environ.get("GOOGLE_GENAI_USE_VERTEXAI", "").lower() == "true"
        
        # Initialize Gemini client
        try:
            if use_vertex_ai:
                logger.bind(tag=TAG).info("检测到 GOOGLE_GENAI_USE_VERTEXAI=True，使用 Vertex AI 模式初始化客户端")
                self.client = genai.Client(
                    http_options=types.HttpOptions(
                        api_version="v1",
                        timeout=30000  # 30秒超时
                    )
                )
            else:
                if not api_key:
                    raise ValueError("Gemini API key is required when not using Vertex AI")
                logger.bind(tag=TAG).info("使用标准 Gemini API 模式初始化客户端")
                self.client = genai.Client(
                    api_key=api_key,
                    http_options=types.HttpOptions(timeout=30000)  # 30秒超时
                )
            logger.bind(tag=TAG).info("Gemini搜索客户端初始化成功")
        except Exception as e:
            logger.bind(tag=TAG).error(f"Gemini搜索客户端初始化失败: {str(e)}")
            raise
    
    async def search_with_gemini(self, query: str, system_prompt: str = None) -> str:
        """使用Gemini 2.5 Flash进行网络搜索"""
        try:
            # 构建搜索工具
            search_tool = Tool(google_search=types.GoogleSearch)
            
            # 默认系统提示
            if not system_prompt:
                system_prompt = (
                    "你是一个专业的搜索助手，请根据用户的搜索需求，使用搜索工具查找相关信息，"
                    "并整理成简洁、准确、有用的回答。请确保信息的时效性和准确性。"
                )
            
            # 创建生成配置
            generation_config = GenerateContentConfig(
                tools=[search_tool],
                response_modalities=["TEXT"],
                thinking_config=types.ThinkingConfig(thinking_budget=0),
                system_instruction=system_prompt
            )
            
            # 生成回答
            response = await self.client.aio.models.generate_content(
                model=self.model,
                contents=query,
                config=generation_config
            )
            
            # 提取回答文本
            response_text = ""
            for part in response.candidates[0].content.parts:
                if hasattr(part, 'text') and part.text:
                    response_text += part.text
            
            return response_text
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"Gemini搜索失败: {str(e)}")
            return f"搜索时出现错误：{str(e)}"

@register_function('search_web', search_web_function_desc, ToolType.SYSTEM_CTL)
def search_web(conn, query: str):
    # 参数验证
    if not query or not isinstance(query, str) or query.strip() == "":
        return ActionResponse(
            Action.REQLLM, "请提供有效的搜索内容", None
        )
    
    # 获取Gemini API配置
    try:
        # 从配置中获取Gemini API密钥
        gemini_config = conn.config.get("providers", {}).get("gemini_search", {})
        api_key = gemini_config.get("api_key")
        model = gemini_config.get("model", "gemini-2.5-flash")
        
        # Check if using Vertex AI
        use_vertex_ai = os.environ.get("GOOGLE_GENAI_USE_VERTEXAI", "").lower() == "true"
        
        if not use_vertex_ai and not api_key:
            logger.bind(tag=TAG).error("Gemini API密钥未配置且未启用Vertex AI")
            return ActionResponse(
                Action.REQLLM, "搜索功能暂时不可用，请联系管理员配置API密钥或启用Vertex AI", None
            )
        
    except Exception as e:
        logger.bind(tag=TAG).error(f"获取配置失败: {str(e)}")
        return ActionResponse(
            Action.REQLLM, "搜索功能配置异常", None
        )
    
    # 记录搜索请求
    logger.bind(tag=TAG).info(f"网络搜索请求: {query}")
    
    try:
        # 创建Gemini搜索客户端
        search_client = GeminiSearchClient(api_key, model)
        
        # 系统提示
        system_prompt = (
            "你是一个专业的搜索助手，请根据用户的搜索需求，使用搜索工具查找相关信息，"
            "并整理成简洁、准确、有用的回答。请确保信息的时效性和准确性。"
        )
        
        # 执行搜索 (使用同步方式运行异步函数)
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(
                search_client.search_with_gemini(query, system_prompt)
            )
        finally:
            loop.close()
        
        # 记录搜索结果
        logger.bind(tag=TAG).info(f"网络搜索完成: {query[:50]}...")
        
        return ActionResponse(Action.REQLLM, result, None)
        
    except Exception as e:
        logger.bind(tag=TAG).error(f"网络搜索执行失败: {str(e)}")
        return ActionResponse(
            Action.REQLLM, f"搜索时遇到问题：{str(e)}", None
        )