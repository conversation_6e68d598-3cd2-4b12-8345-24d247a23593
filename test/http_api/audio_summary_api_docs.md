# 音频摘要接口文档

## 接口概述

POST `/api/v1/audio_summary` 接口用于上传会议录音分片，生成累积的会议纪要。支持分片上传，每个分片通过 `task_id` 关联到同一次会议。

## 接口变更说明

**新版本接口**：客户端需要上传音频文件 + JSON 数据，服务端自动从 CDN 读取之前分片的会议纪要。

**旧版本兼容性**：如果客户端未上传 JSON 数据，服务端将返回 "客户端请升级到最新版" 错误提示。

## 接口详情

### 请求地址
```
POST /api/v1/audio_summary
```

### 请求头
```
Content-Type: multipart/form-data
client-id: {客户端ID}
device-id: {设备ID}
authorization: Bearer {认证令牌}
```

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| audio | File | 是 | MP3 音频文件（最大 200MB） |
| json | String | 是 | JSON 格式的任务信息 |

#### JSON 参数格式

**第一个分片（无 task_id）**：
```json
{}
```

**后续分片（包含 task_id）**：
```json
{
  "task_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### 响应格式

#### 成功响应

**HTTP 状态码**: 200

```json
{
  "success": true,
  "message": "音频摘要任务已启动",
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "processing",
    "filename": "meeting_part1.mp3",
    "file_size": 1024000
  },
  "timestamp": "2024-06-26T14:30:00.000Z"
}
```

#### 错误响应

**HTTP 状态码**: 400

```json
{
  "success": false,
  "message": "客户端请升级到最新版",
  "timestamp": "2024-06-26T14:30:00.000Z"
}
```

## 分片上传逻辑示例

### 场景：1小时会议录音，分为6个10分钟分片

#### 1. 上传第一个分片

**请求**:
```bash
curl -X POST "https://api.example.com/api/v1/audio_summary" \
  -H "client-id: your-client-id" \
  -H "device-id: your-device-id" \
  -H "authorization: Bearer your-token" \
  -F "audio=@meeting_part1.mp3" \
  -F "json={}"
```

**响应**:
```json
{
  "success": true,
  "message": "音频摘要任务已启动",
  "data": {
    "task_id": "meeting-001",
    "status": "processing",
    "filename": "meeting_part1.mp3",
    "file_size": 2048000
  },
  "timestamp": "2024-06-26T14:30:00.000Z"
}
```

#### 2. 上传第二个分片

**请求**:
```bash
curl -X POST "https://api.example.com/api/v1/audio_summary" \
  -H "client-id: your-client-id" \
  -H "device-id: your-device-id" \
  -H "authorization: Bearer your-token" \
  -F "audio=@meeting_part2.mp3" \
  -F "json={\"task_id\": \"meeting-001\"}"
```

**响应**:
```json
{
  "success": true,
  "message": "音频摘要任务已启动",
  "data": {
    "task_id": "meeting-001",
    "status": "processing",
    "filename": "meeting_part2.mp3",
    "file_size": 2048000
  },
  "timestamp": "2024-06-26T14:40:00.000Z"
}
```

#### 3. 后续分片按相同模式上传

每次上传都使用相同的 `task_id: "meeting-001"`，服务端会：
1. 从 CDN 读取之前所有分片生成的累积会议纪要
2. 将新音频内容与已有纪要合并
3. 生成新的累积会议纪要
4. 上传到 CDN 并更新记录

## 查询任务状态

### 请求地址
```
GET /api/v1/audio_summary/{task_id}
```

### 响应示例

**处理中**:
```json
{
  "success": true,
  "message": "任务状态查询成功",
  "data": {
    "task_id": "meeting-001",
    "status": "processing",
    "last_updated": "2024-06-26T14:30:00.000Z"
  },
  "timestamp": "2024-06-26T14:31:00.000Z"
}
```

**已完成**:
```json
{
  "success": true,
  "message": "任务状态查询成功",
  "data": {
    "task_id": "meeting-001",
    "status": "completed",
    "last_updated": "2024-06-26T14:32:00.000Z",
    "summary": "# 会议纪要\n\n## 基本信息\n- 会议时间：2024年06月26日\n...",
    "summary_length": 1500,
    "version": 2
  },
  "timestamp": "2024-06-26T14:33:00.000Z"
}
```

## 错误处理

### 常见错误

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| "客户端请升级到最新版" | 未上传 JSON 参数 | 确保请求包含 json 字段 |
| "JSON格式错误" | JSON 格式不正确 | 检查 JSON 语法 |
| "只支持MP3格式的音频文件" | 文件格式错误 | 使用 MP3 格式文件 |
| "文件大小超过限制" | 文件过大 | 文件大小不超过 200MB |
| "认证失败" | 认证信息错误 | 检查 client-id 和 authorization |

### HTTP 状态码

- **200**: 请求成功
- **400**: 请求参数错误
- **401**: 认证失败
- **500**: 服务器内部错误

## 最佳实践

### 1. 分片策略
- 建议每个分片 5-10 分钟，文件大小 10-50MB
- 避免单个分片过大（>100MB）或过小（<1MB）

### 2. 错误重试
```javascript
async function uploadAudioFragment(audioFile, taskId = null) {
  const formData = new FormData();
  formData.append('audio', audioFile);
  
  const jsonData = taskId ? { task_id: taskId } : {};
  formData.append('json', JSON.stringify(jsonData));
  
  try {
    const response = await fetch('/api/v1/audio_summary', {
      method: 'POST',
      headers: {
        'client-id': 'your-client-id',
        'device-id': 'your-device-id',
        'authorization': 'Bearer your-token'
      },
      body: formData
    });
    
    const result = await response.json();
    
    if (result.success) {
      return result.data.task_id; // 用于后续分片
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('上传失败:', error);
    // 实现重试逻辑
    throw error;
  }
}
```

### 3. 任务状态轮询
```javascript
async function pollTaskStatus(taskId) {
  let attempts = 0;
  const maxAttempts = 60; // 最多轮询 10 分钟
  
  while (attempts < maxAttempts) {
    try {
      const response = await fetch(`/api/v1/audio_summary/${taskId}`, {
        headers: {
          'client-id': 'your-client-id',
          'device-id': 'your-device-id',
          'authorization': 'Bearer your-token'
        }
      });
      
      const result = await response.json();
      
      if (result.data.status === 'completed') {
        return result.data.summary;
      } else if (result.data.status === 'failed') {
        throw new Error(result.data.error);
      }
      
      // 等待 10 秒后继续轮询
      await new Promise(resolve => setTimeout(resolve, 10000));
      attempts++;
      
    } catch (error) {
      console.error('查询状态失败:', error);
      attempts++;
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
  
  throw new Error('任务处理超时');
}
```

### 4. 完整的分片上传流程
```javascript
async function uploadMeetingRecording(audioFragments) {
  let taskId = null;
  
  // 上传所有分片
  for (let i = 0; i < audioFragments.length; i++) {
    const fragment = audioFragments[i];
    
    try {
      // 第一个分片不传 task_id，后续分片传 task_id
      const response = await uploadAudioFragment(fragment, taskId);
      
      if (i === 0) {
        taskId = response.task_id; // 保存会议任务ID
      }
      
      console.log(`分片 ${i + 1} 上传成功`);
      
      // 等待当前分片处理完成（推荐做法，避免内容丢失）
      await pollTaskStatus(taskId);
      
    } catch (error) {
      console.error(`分片 ${i + 1} 上传失败:`, error);
      // 根据需要实现重试逻辑
    }
  }
  
  // 获取最终的完整纪要
  if (taskId) {
    const finalSummary = await pollTaskStatus(taskId);
    console.log('会议纪要生成完成:', finalSummary);
    return finalSummary;
  }
  
  throw new Error('没有成功上传任何分片');
}
```

## 注意事项

1. **task_id 管理**: 确保同一次会议的所有分片使用相同的 `task_id`
2. **处理顺序**: 分片可以并发上传，服务端会按接收顺序处理
3. **缓存刷新**: 服务端会为每次纪要生成新的 CDN URL，避免缓存问题
4. **数据安全**: 音频文件和纪要都会加密存储在 CDN
5. **过期时间**: 任务数据在 Redis 中保存 7 天后自动过期