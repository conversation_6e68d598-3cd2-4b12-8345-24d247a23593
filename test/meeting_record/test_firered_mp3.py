#!/usr/bin/env python3
"""
FireRedASR MP3处理测试脚本
测试MP3文件→ASR转文本→Gemini生成会议纪要的完整流程和耗时
"""

import os
import sys
import argparse
import time
import json
import asyncio
from datetime import datetime
from typing import Optional
import traceback
from pydub import AudioSegment

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config.config_loader import load_config
from config.logger import setup_logging
from core.providers.asr.firered import ASRProvider
from google import genai
from google.genai import types

logger = setup_logging()
TAG = __name__


class MP3ToSummaryTester:
    """MP3转会议纪要测试器"""
    
    def __init__(self, config_path: str = None):
        """初始化测试器"""
        self.config = load_config()
        logger.info("配置加载成功")
        
        # 初始化FireRedASR
        firered_config = self.config.get("ASR", {}).get("FireRedASR", {})
        if not firered_config:
            raise ValueError("未找到FireRedASR配置")
        
        self.asr = ASRProvider(firered_config, delete_audio_file=False)
        logger.info("FireRedASR初始化成功")
        
        # 初始化Gemini客户端（复用AudioProcessor的配置）
        llm_config = self.config.get("LLM", {}).get("GeminiLLM", {})
        if not llm_config:
            raise ValueError("未找到GeminiLLM配置")
            
        use_vertex_ai = os.environ.get("GOOGLE_GENAI_USE_VERTEXAI", "").lower() == "true"
        self.api_key = llm_config.get("api_key")
        self.model = llm_config.get("model", "gemini-2.5-flash")
        
        if use_vertex_ai:
            logger.info("使用Vertex AI模式初始化Gemini客户端")
            self.gemini_client = genai.Client(
                http_options=types.HttpOptions(
                    api_version="v1",
                    timeout=10*60*1000  # 10分钟超时
                )
            )
        else:
            if not self.api_key:
                raise ValueError("Gemini API key is required when not using Vertex AI")
            logger.info("使用标准Gemini API模式初始化客户端")
            self.gemini_client = genai.Client(
                api_key=self.api_key,
                http_options=types.HttpOptions(timeout=10*60*1000)
            )
        
        logger.info(f"Gemini客户端初始化成功，模型: {self.model}")
    
    def validate_mp3_file(self, mp3_path: str) -> bool:
        """验证MP3文件"""
        if not os.path.exists(mp3_path):
            logger.error(f"文件不存在: {mp3_path}")
            return False
        
        if not mp3_path.lower().endswith('.mp3'):
            logger.error(f"文件不是MP3格式: {mp3_path}")
            return False
        
        file_size = os.path.getsize(mp3_path)
        max_size = 100 * 1024 * 1024  # 100MB
        if file_size >= max_size:
            logger.error(f"文件过大 ({file_size} bytes)，超过100MB限制")
            return False
        
        logger.info(f"MP3文件验证通过: {mp3_path} ({file_size} bytes)")
        return True
    
    def convert_mp3_to_wav(self, mp3_path: str) -> str:
        """将MP3文件转换为WAV格式"""
        logger.info(f"开始转换MP3到WAV: {mp3_path}")
        
        try:
            # 生成WAV文件路径
            wav_path = mp3_path.replace('.mp3', '_converted.wav')
            
            # 使用pydub转换音频格式
            audio = AudioSegment.from_mp3(mp3_path)
            
            # 转换为FireRedASR需要的格式：单声道、16kHz采样率
            audio = audio.set_channels(1).set_frame_rate(16000)
            
            # 导出为WAV文件
            audio.export(wav_path, format="wav")
            
            logger.info(f"MP3转WAV成功: {wav_path}")
            return wav_path
            
        except Exception as e:
            logger.error(f"MP3转WAV失败: {str(e)}")
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise
    
    async def test_firered_asr(self, mp3_path: str) -> tuple[Optional[str], float]:
        """测试FireRedASR处理MP3文件"""
        logger.info(f"开始FireRedASR识别测试: {mp3_path}")
        wav_path = None
        
        try:
            start_time = time.time()
            
            # 将MP3转换为WAV格式
            wav_path = self.convert_mp3_to_wav(mp3_path)
            
            # 创建session_id
            session_id = f"test_{int(time.time())}"
            
            # 使用FireRedASR的transcribe方法处理WAV文件
            uttid = f"test_session_{session_id}"
            results = self.asr.model.transcribe(
                [uttid],
                [wav_path],  # 传入WAV文件路径
                {
                    "use_gpu": self.asr.use_gpu,
                    "device": self.asr.device,
                    "beam_size": 1,
                    "nbest": 1,
                    "decode_max_len": 0,
                    "softmax_smoothing": 1.0,
                    "aed_length_penalty": 0.0,
                    "eos_penalty": 1.0,
                    "decode_min_len": 0,
                    "repetition_penalty": 1.0,
                    "llm_length_penalty": 0.0,
                    "temperature": 1.0
                }
            )
            
            asr_time = time.time() - start_time
            
            if results and len(results) > 0:
                text = results[0]["text"]
                logger.info(f"ASR识别成功，耗时: {asr_time:.3f}s")
                logger.info(f"识别文本长度: {len(text)} 字符")
                logger.info(f"识别文本内容: {text[:200]}...")  # 显示前200字符
                return text, asr_time
            else:
                logger.error("ASR识别失败：无结果返回")
                return None, asr_time
                
        except Exception as e:
            asr_time = time.time() - start_time
            logger.error(f"ASR识别异常: {str(e)}")
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            return None, asr_time
        finally:
            # 清理临时WAV文件
            if wav_path and os.path.exists(wav_path):
                try:
                    os.remove(wav_path)
                    logger.info(f"已清理临时WAV文件: {wav_path}")
                except Exception as e:
                    logger.warning(f"清理临时WAV文件失败: {str(e)}")
    
    async def test_gemini_text_summary(self, text: str) -> tuple[Optional[str], float]:
        """测试Gemini基于文本生成会议纪要"""
        logger.info("开始Gemini文本生成测试")
        
        meeting_time_str = datetime.now().strftime("%Y年%m月%d日")
        
        # 复用AudioProcessor中的prompt模板（文本版本）
        system_prompt = f"""你是一个专业的会议纪要整理助手，需要将文本内容整理成规范的会议纪要。你背后的模型是APUS公司训练的APUS大模型。

要求：
1. 仔细分析文本内容，提取关键信息
2. 按照标准会议纪要格式组织内容
3. 包括：会议主题、主要讨论内容、决议事项、行动计划等
4. 语言要简洁明了，条理清晰
5. 突出重点决议和后续行动
6. 会议时间请固定使用：{meeting_time_str}

会议纪要格式：
# 会议纪要

## 基本信息
- 会议时间：{meeting_time_str}
- 会议主题：[从文本中提取]

## 主要讨论内容
[按要点列出主要讨论的内容]

## 决议事项
[列出会议中达成的决议]

## 行动计划
[列出后续需要执行的行动项目，包括负责人和时间节点]

## 其他事项
[其他需要记录的内容]

请严格按照以上格式整理会议纪要，特别注意会议时间必须使用指定的时间。如果文本质量不佳或信息不完整，请根据现有信息尽力整理；实在提取不出任何信息，直接返回"NO_MEETING_CONTENT"
"""

        user_prompt = f"请分析这段会议文本，生成完整的会议纪要。会议时间请使用：{meeting_time_str}\n\n会议文本内容：\n{text}"
        
        try:
            start_time = time.time()
            
            # 创建生成配置
            generation_config = types.GenerateContentConfig(
                temperature=0.1,
                system_instruction=system_prompt,
                thinking_config=types.ThinkingConfig(thinking_budget=0)
            )
            
            logger.info(f"开始调用Gemini API - 模型: {self.model}")
            logger.info(f"输入文本长度: {len(user_prompt)} 字符")
            
            # 调用Gemini API
            response = await self.gemini_client.aio.models.generate_content(
                model=self.model,
                contents=[user_prompt],
                config=generation_config
            )
            
            gemini_time = time.time() - start_time
            
            if hasattr(response, 'text') and response.text:
                result_text = response.text.strip()
                
                # 检查是否返回了NO_MEETING_CONTENT
                if "NO_MEETING_CONTENT" in result_text:
                    logger.warning(f"Gemini检测到文本中没有会议内容")
                    return None, gemini_time
                
                logger.info(f"Gemini生成成功，耗时: {gemini_time:.3f}s")
                logger.info(f"生成纪要长度: {len(result_text)} 字符")
                return result_text, gemini_time
            else:
                logger.error("Gemini生成失败：无有效响应")
                return None, gemini_time
                
        except Exception as e:
            gemini_time = time.time() - start_time
            logger.error(f"Gemini生成异常: {str(e)}")
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            return None, gemini_time
    
    async def run_test(self, mp3_path: str) -> dict:
        """运行完整测试流程"""
        logger.info(f"开始完整测试流程: {mp3_path}")
        
        result = {
            "file_path": mp3_path,
            "file_size": 0,
            "start_time": datetime.now().isoformat(),
            "asr_result": None,
            "asr_time": 0,
            "gemini_result": None,
            "gemini_time": 0,
            "total_time": 0,
            "success": False,
            "error": None
        }
        
        try:
            start_time = time.time()
            
            # 1. 验证MP3文件
            if not self.validate_mp3_file(mp3_path):
                result["error"] = "MP3文件验证失败"
                return result
            
            result["file_size"] = os.path.getsize(mp3_path)
            
            # 2. 测试FireRedASR
            asr_text, asr_time = await self.test_firered_asr(mp3_path)
            result["asr_result"] = asr_text
            result["asr_time"] = asr_time
            
            if not asr_text:
                result["error"] = "ASR识别失败"
                result["total_time"] = time.time() - start_time
                return result
            
            # 3. 测试Gemini文本生成
            summary, gemini_time = await self.test_gemini_text_summary(asr_text)
            result["gemini_result"] = summary
            result["gemini_time"] = gemini_time
            
            result["total_time"] = time.time() - start_time
            result["success"] = summary is not None
            
            if not summary:
                result["error"] = "Gemini生成会议纪要失败"
            
            logger.info(f"测试完成，总耗时: {result['total_time']:.3f}s")
            return result
            
        except Exception as e:
            result["total_time"] = time.time() - start_time
            result["error"] = str(e)
            logger.error(f"测试流程异常: {str(e)}")
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            return result


def print_test_report(result: dict):
    """打印测试报告"""
    print("\n" + "="*80)
    print("FireRedASR MP3处理测试报告")
    print("="*80)
    
    print(f"文件路径: {result['file_path']}")
    print(f"文件大小: {result['file_size']:,} bytes ({result['file_size']/1024/1024:.2f} MB)")
    print(f"测试时间: {result['start_time']}")
    print(f"测试结果: {'成功' if result['success'] else '失败'}")
    
    if result['error']:
        print(f"错误信息: {result['error']}")
    
    print("\n" + "-"*40)
    print("性能统计:")
    print("-"*40)
    print(f"ASR识别耗时: {result['asr_time']:.3f}s")
    print(f"Gemini生成耗时: {result['gemini_time']:.3f}s")
    print(f"总处理时间: {result['total_time']:.3f}s")
    
    if result['asr_result']:
        print(f"\nASR识别文本长度: {len(result['asr_result'])} 字符")
        print("ASR识别文本预览:")
        print("-"*40)
        print(result['asr_result'][:500] + ("..." if len(result['asr_result']) > 500 else ""))
    
    if result['gemini_result']:
        print(f"\n会议纪要长度: {len(result['gemini_result'])} 字符")
        print("会议纪要内容:")
        print("-"*40)
        print(result['gemini_result'])
    
    print("\n" + "="*80)


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='FireRedASR MP3处理测试脚本')
    parser.add_argument('mp3_file', help='MP3文件路径')
    parser.add_argument('--output', '-o', help='结果输出JSON文件路径（可选）')
    
    args = parser.parse_args()
    
    try:
        # 初始化测试器
        tester = MP3ToSummaryTester()
        
        # 运行测试
        result = await tester.run_test(args.mp3_file)
        
        # 打印报告
        print_test_report(result)
        
        # 保存结果到文件
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n测试结果已保存到: {args.output}")
        
        # 根据测试结果返回相应的退出码
        sys.exit(0 if result['success'] else 1)
        
    except Exception as e:
        logger.error(f"测试脚本执行失败: {str(e)}")
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        sys.exit(2)


if __name__ == "__main__":
    asyncio.run(main())