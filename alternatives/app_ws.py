#!/usr/bin/env python3
"""
独立的WebSocket服务器
从原app.py中分离出WebSocket服务部分
"""

import argparse
import asyncio
import signal
import sys
from config.settings import load_config
from core.websocket_server import WebSocketServer
from core.ota_server import SimpleOtaServer
from core.utils.util import check_ffmpeg_installed, get_local_ip
from config.logger import setup_logging
from core.utils.redis_client import RedisClient
from aioconsole import ainput

TAG = __name__
logger = setup_logging()

async def wait_for_exit() -> None:
    """等待退出信号"""
    loop = asyncio.get_running_loop()
    stop_event = asyncio.Event()

    if sys.platform != "win32":  # Unix / macOS
        for sig in (signal.SIGINT, signal.SIGTERM):
            loop.add_signal_handler(sig, stop_event.set)
        await stop_event.wait()
    else:
        # Windows
        try:
            await asyncio.Future()
        except KeyboardInterrupt:
            pass

async def monitor_stdin():
    """监控标准输入"""
    # 检查是否有有效的终端连接
    if not sys.stdin.isatty():
        logger.bind(tag=TAG).info("未检测到交互式终端，跳过stdin监控")
        return
        
    try:
        while True:
            try:
                await asyncio.wait_for(ainput(), timeout=60.0)
            except asyncio.TimeoutError:
                continue
            except EOFError:
                logger.bind(tag=TAG).info("检测到EOF，WebSocket服务准备退出...")
                break
            except Exception as e:
                logger.bind(tag=TAG).error(f"标准输入监控错误: {e}")
                break
    except Exception as e:
        logger.bind(tag=TAG).error(f"标准输入监控致命错误: {e}")
        raise

def check_redis_server(config):
    """检查Redis服务器连接"""
    redis_client = RedisClient(config)
    redis_client.check_redis_sever()

async def cleanup_resources():
    """清理资源"""
    logger.bind(tag=TAG).info("资源清理完成")

async def main(ws_port=None):
    """WebSocket服务主函数"""
    check_ffmpeg_installed()
    config = load_config()
    check_redis_server(config)

    # 如果指定了端口，覆盖配置文件中的端口
    if ws_port is not None:
        if "server" not in config:
            config["server"] = {}
        config["server"]["port"] = ws_port

    # 添加 stdin 监控任务（仅当有终端时）
    stdin_task = None
    if sys.stdin.isatty():
        stdin_task = asyncio.create_task(monitor_stdin())

    # 启动 WebSocket 服务器
    ws_server = WebSocketServer(config)
    ws_task = asyncio.create_task(ws_server.start())

    # 启动 OTA 服务器（如果需要）
    ota_task = None
    read_config_from_api = config.get("read_config_from_api", False)
    if not read_config_from_api:
        ota_server = SimpleOtaServer(config)
        ota_task = asyncio.create_task(ota_server.start())
        
        logger.bind(tag=TAG).info(
            "OTA接口是\t\thttp://{}:{}/xiaozhi/ota/",
            get_local_ip(),
            config["server"]["ota_port"],
        )

    # 获取WebSocket配置
    websocket_port = int(config.get("server", {}).get("port", 8000))
    
    logger.bind(tag=TAG).info(
        "Websocket地址是\tws://{}:{}/xiaozhi/v1/",
        get_local_ip(),
        websocket_port,
    )
    
    logger.bind(tag=TAG).info("WebSocket服务器已启动")

    try:
        await wait_for_exit()
    except asyncio.CancelledError:
        logger.bind(tag=TAG).info("WebSocket服务被取消，清理资源中...")
    finally:
        # 取消所有任务
        if stdin_task:
            stdin_task.cancel()
        ws_task.cancel()
        if ota_task:
            ota_task.cancel()

        # 等待任务终止
        tasks_to_wait = [ws_task]
        if stdin_task:
            tasks_to_wait.append(stdin_task)
        if ota_task:
            tasks_to_wait.append(ota_task)

        try:
            await asyncio.wait_for(
                asyncio.gather(*tasks_to_wait, return_exceptions=True),
                timeout=10.0
            )
        except asyncio.TimeoutError:
            logger.bind(tag=TAG).warning("任务清理超时")

        # 清理资源
        await cleanup_resources()
        logger.bind(tag=TAG).info("WebSocket服务已清理完成")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='WebSocket服务器')
    parser.add_argument('--ws_port', type=int, help='WebSocket服务端口')
    args = parser.parse_args()

    asyncio.run(main(ws_port=args.ws_port))